#!/usr/bin/env python3
"""
Script to get chat information from Telegram bot
"""

import asyncio
import os
import sys
import json

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def get_chat_info():
    """Get chat information"""
    
    # Get credentials from environment
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    
    if not token:
        print("❌ Missing TELEGRAM_BOT_TOKEN")
        return
    
    client = TelegramAPIClient(token)
    
    print("🔍 Getting bot information...")
    
    try:
        import aiohttp

        async with aiohttp.ClientSession() as session:
            # Get bot info
            async with session.get(f"https://api.telegram.org/bot{token}/getMe") as response:
                if response.status == 200:
                    bot_info = await response.json()
                    print("🤖 Bot Info:")
                    print(json.dumps(bot_info, indent=2))
                else:
                    print(f"❌ Error getting bot info: {response.status}")

            # Get updates to see recent messages
            async with session.get(f"https://api.telegram.org/bot{token}/getUpdates") as response:
                if response.status == 200:
                    updates = await response.json()
                    print("\n📨 Recent Updates:")
                    if updates.get('result'):
                        for update in updates['result'][-5:]:  # Last 5 updates
                            print(f"Update ID: {update.get('update_id')}")
                            if 'message' in update:
                                msg = update['message']
                                print(f"  Chat ID: {msg['chat']['id']}")
                                print(f"  Chat Type: {msg['chat']['type']}")
                                print(f"  From: {msg['from']['first_name']} ({msg['from']['id']})")
                                print(f"  Text: {msg.get('text', 'N/A')}")
                                print("  ---")
                    else:
                        print("No recent updates found")
                else:
                    print(f"❌ Error getting updates: {response.status}")

    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    asyncio.run(get_chat_info())
