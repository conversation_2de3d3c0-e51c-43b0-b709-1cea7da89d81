# 🏗️ Kế hoạch Tái cấu trúc: <PERSON><PERSON> thống 2 Docker Images

## 📊 Tổng quan Kiến trúc <PERSON>i

### 🎯 Mục tiêu
- **Tập trung hóa**: Tất cả logic trong `bot.sh`
- **Tách biệt**: 2 Docker images chuyên biệt
- **Quản lý từ xa**: Telegram bot quản lý tất cả trader bots

### 🐳 Docker Images

#### 1. **TelegramBot Image** (`autotrader-telegram`)
```
Thành phần:
├── bot.sh (tích hợp tất cả functions)
├── Python 3.13 + python-telegram-bot
├── Docker SDK for container management
├── Credential encryption/decryption
└── Configuration templates

Chức năng:
- Telegram bot interface
- Credential management (encrypted storage)
- Bot lifecycle management (create/start/stop/restart)
- Bulk operations (multiple bots)
- Real-time monitoring & notifications
- Config template management
```

#### 2. **TraderBot Image** (`autotrader-bot`)
```
Thành phần:
├── Python trading application
├── Exchange connectors (Bybit, Binance)
├── Trading strategies & indicators
├── Risk management
└── WebSocket event handlers

Chức năng:
- Execute trading strategies
- Market data processing
- Order management
- Risk monitoring
- Performance tracking
```

## 🔄 Communication Architecture

### Data Flow
```
Telegram User → Telegram Bot → Docker API → Trader Bot Containers
                     ↓                            ↓
               Shared Volumes ←────────────────────┘
```

### Shared Volumes
- **`configs/`**: Trading configurations
- **`credentials/`**: Encrypted API credentials  
- **`data/`**: Trading data & history
- **`logs/`**: Application logs

## 🔧 Integration Plan

### Phase 1: Consolidation
1. Merge telegram functionality into `bot.sh`
2. Add Docker API management
3. Implement encrypted credential storage

### Phase 2: Docker Images
1. Create traderbot Dockerfile
2. Create telegrambot Dockerfile
3. Setup communication layer

### Phase 3: Advanced Features
1. Bulk operations
2. Real-time monitoring
3. Strategy templates

## 📋 Key Features

### Telegram Commands (trong bot.sh)
```bash
# Credential Management
/setkey <key> <secret>              # Quick credential setup
/addcreds                          # Advanced credential wizard
/listcreds                         # List all credentials

# Bot Management  
/createbot <symbol> <amount>       # Create new trading bot
/startbot <symbol>                 # Start existing bot
/stopbot <symbol>                  # Stop specific bot
/listbots                          # List all bots
/logs <symbol>                     # View bot logs

# Bulk Operations
/startall                          # Start all bots
/stopall                           # Stop all bots
/status                            # System overview

# Configuration
/templates                         # List config templates
/newconfig <name>                  # Create config from template
```

### Container Management
```bash
# TelegramBot sẽ quản lý TraderBot containers qua Docker API
docker run autotrader-bot:latest --symbol HYPER/USDT:USDT --amount 100
docker ps                         # List trader containers
docker logs <container>           # Get trader logs
docker stop <container>           # Stop trader
```

## 🔐 Security Features

### Credential Management
- **Encryption**: AES-256 encryption cho API credentials
- **Isolation**: Mỗi bot có credential riêng
- **Access Control**: Telegram user authentication

### Data Protection
- **Volume Permissions**: Restricted access
- **Secrets Management**: No credentials in logs
- **Audit Trail**: All operations logged

## 📈 Monitoring & Notifications

### Real-time Updates
- Bot status changes
- Trade executions
- P&L updates
- Error alerts

### Performance Metrics
- Active bots count
- Total P&L
- Success rate
- System resource usage

## 🚀 Deployment Strategy

### Development
```bash
# Build images
docker build -f Dockerfile.telegram -t autotrader-telegram .
docker build -f Dockerfile.trader -t autotrader-bot .

# Run telegram bot
docker run -d autotrader-telegram

# Trader bots được tạo tự động qua telegram commands
```

### Production
```yaml
# docker-compose.yml
version: '3.8'
services:
  telegram-bot:
    image: autotrader-telegram:latest
    volumes:
      - ./data:/app/data
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
```

## ✅ Success Metrics

1. **Single Point of Control**: Tất cả thao tác qua Telegram
2. **Scalability**: Dễ dàng tạo/quản lý nhiều bots
3. **Maintainability**: Logic tập trung trong bot.sh
4. **Security**: Credentials được mã hóa
5. **Monitoring**: Real-time visibility của tất cả bots 