#!/bin/bash
echo "🔄 Restarting Telegram bot..."

# Stop and remove existing container
echo "🛑 Stopping existing container..."
docker stop autotrader-telegram 2>/dev/null || true
docker rm autotrader-telegram 2>/dev/null || true

# Build new image with correct Dockerfile path
echo "🏗️ Building new image..."
docker build -t autotrader-telegram -f Dockerfile.telegram .

if [ $? -eq 0 ]; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

# Get Docker group ID from host
DOCKER_GID=$(stat -c '%g' /var/run/docker.sock)

# Run new container with Docker socket mounted
echo "🚀 Starting new container..."
docker run -d \
    --name autotrader-telegram \
    --env-file .env \
    -v $(pwd):/app \
    -v /var/run/docker.sock:/var/run/docker.sock \
    --group-add $DOCKER_GID \
    autotrader-telegram

if [ $? -eq 0 ]; then
    echo "✅ Container started"
else
    echo "❌ Failed to start container"
    exit 1
fi

# Show logs
echo "📋 Container logs:"
sleep 3
docker logs autotrader-telegram --tail 20

echo ""
echo "✅ Bot restarted successfully!"
echo "💬 Test the bot by sending /list to your Telegram bot"
echo "🔍 Check logs with: docker logs autotrader-telegram --tail 50"
