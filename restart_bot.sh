#!/bin/bash
echo "🔄 Restarting Telegram bot..."

# Stop and remove existing container
echo "🛑 Stopping existing container..."
docker stop autotrader-telegram 2>/dev/null || true
docker rm autotrader-telegram 2>/dev/null || true

# Build new image
echo "🏗️ Building new image..."
if docker build -t autotrader-telegram -f docker/telegram/Dockerfile .; then
    echo "✅ Build successful"
else
    echo "❌ Build failed"
    exit 1
fi

# Run new container
echo "🚀 Starting new container..."
if docker run -d --name autotrader-telegram --env-file .env -v $(pwd):/app autotrader-telegram; then
    echo "✅ Container started"
else
    echo "❌ Failed to start container"
    exit 1
fi

# Show logs
echo "📋 Container logs:"
sleep 3
docker logs autotrader-telegram --tail 15

echo "✅ Bot restarted successfully!"
echo "💬 Test the bot by sending /start to your Telegram bot"
