#!/bin/bash
echo "🔄 Restarting Telegram bot..."

# Stop and remove existing container
docker stop autotrader-telegram 2>/dev/null || true
docker rm autotrader-telegram 2>/dev/null || true

# Build new image
echo "🏗️ Building new image..."
docker build -t autotrader-telegram -f docker/telegram/Dockerfile .

# Run new container
echo "🚀 Starting new container..."
docker run -d --name autotrader-telegram --env-file .env -v $(pwd):/app autotrader-telegram

# Show logs
echo "📋 Container logs:"
sleep 2
docker logs autotrader-telegram --tail 10

echo "✅ Bot restarted successfully!"
