#!/bin/bash

# Debug check_docker function

check_docker() {
    echo "🔍 Debug: Starting check_docker function"
    
    # If running inside Docker container, assume Docker is available via socket
    if [ -f /.dockerenv ]; then
        echo "🐳 Running inside Docker container"
        # Always use sudo in container since docker socket permissions are complex
        export DOCKER_CMD="sudo docker"
        echo "🔧 Set DOCKER_CMD to: $DOCKER_CMD"
        
        # Test the docker command
        echo "🧪 Testing Docker command..."
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        echo "✅ Docker command works!"
        return 0
    fi

    # Check if Docker socket is available
    if [ -S /var/run/docker.sock ]; then
        export DOCKER_CMD="docker"
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        return 0
    fi

    if ! command -v docker >/dev/null 2>&1; then
        echo "❌ Docker not found. Please install Docker."
        return 1
    fi

    export DOCKER_CMD="docker"
    if ! $DOCKER_CMD version >/dev/null 2>&1; then
        echo "❌ Cannot connect to Docker daemon."
        return 1
    fi

    return 0
}

echo "🧪 Testing check_docker function..."
if check_docker; then
    echo "✅ check_docker passed"
    echo "🐳 DOCKER_CMD: $DOCKER_CMD"
    echo "📊 Testing Docker list:"
    $DOCKER_CMD ps --format "{{.Names}}"
else
    echo "❌ check_docker failed"
fi
