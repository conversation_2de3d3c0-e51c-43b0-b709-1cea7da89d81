# 🏠 AutoTrader Local Testing Guide

Hướng dẫn sử dụng `bot-local.sh` để test và phát triển AutoTrader với Docker images local.

## 📋 Tổng quan

Script `bot-local.sh` được thiết kế để:
- ✅ Build và test Docker images locally (không cần registry)
- ✅ Debug và phát triển dễ dàng hơn
- ✅ Chạy test suite tự động
- ✅ Hỗ trợ interactive debugging
- ✅ Logging chi tiết với màu sắc

## 🚀 Bắt đầu nhanh

### 1. Kiểm tra môi trường
```bash
./bot-local.sh status
```

### 2. Build images
```bash
# Build tất cả images
./bot-local.sh build both

# Hoặc build riêng lẻ
./bot-local.sh build telegram
./bot-local.sh build trader
```

### 3. Test trading bot
```bash
# Start bot với test mode
./bot-local.sh start hyper --amount 10 --test-mode --debug

# Xem logs real-time
./bot-local.sh logs hyper 50 --follow

# Debug container
./bot-local.sh debug hyper

# Stop bot
./bot-local.sh stop hyper
```

## 🛠️ Các lệnh chính

### Build Commands
```bash
./bot-local.sh build [telegram|trader|both]  # Build images
./bot-local.sh rebuild [mode]                # Force rebuild
```

### Testing Commands
```bash
./bot-local.sh start <symbol> [options]      # Start trading bot
./bot-local.sh stop <symbol>                 # Stop trading bot
./bot-local.sh restart <symbol> [--rebuild]  # Restart (option rebuild)
./bot-local.sh debug <symbol>                # Interactive shell
./bot-local.sh test                          # Chạy test suite
```

### Utility Commands
```bash
./bot-local.sh list                          # List containers
./bot-local.sh status                        # Environment status
./bot-local.sh logs <symbol> [lines]         # Show logs
./bot-local.sh cleanup [--force]             # Clean up resources
```

## 📊 Ví dụ sử dụng

### 1. Development Workflow
```bash
# Kiểm tra environment
./bot-local.sh status

# Build images nếu cần
./bot-local.sh build both

# Start bot với debug mode
./bot-local.sh start hyper --amount 10 --test-mode --debug

# Monitor logs
./bot-local.sh logs hyper 100 --follow

# Debug nếu có lỗi
./bot-local.sh debug hyper

# Restart với rebuild code mới
./bot-local.sh restart hyper --rebuild
```

### 2. Testing Multiple Bots
```bash
# Start nhiều bots
./bot-local.sh start hyper --amount 10 --test-mode
./bot-local.sh start btc --amount 50 --test-mode
./bot-local.sh start eth --amount 25 --test-mode

# List tất cả
./bot-local.sh list

# Stop tất cả
./bot-local.sh stop hyper
./bot-local.sh stop btc  
./bot-local.sh stop eth
```

### 3. Comprehensive Testing
```bash
# Chạy test suite hoàn chỉnh
./bot-local.sh test

# Clean up sau khi test
./bot-local.sh cleanup --force
```

## 🎯 Tính năng đặc biệt

### 1. Auto-rebuild on start
```bash
# Tự động rebuild image khi start nếu cần
./bot-local.sh start hyper --rebuild --debug
```

### 2. Interactive Debugging
```bash
# Vào shell của container để debug
./bot-local.sh debug hyper

# Trong container:
ps aux                    # Check processes
cat /app/logs/trading.log # Check logs
python -c "import sys; print(sys.path)"  # Check Python
```

### 3. Enhanced Logging
```bash
# Debug mode với logs chi tiết
./bot-local.sh start hyper --debug --test-mode

# Follow logs real-time
./bot-local.sh logs hyper 200 --follow
```

### 4. Volume Mounting
Script tự động mount các thư mục:
- `./logs` → `/app/logs` (read-write)
- `./data` → `/app/data` (read-write)  
- `./configs` → `/app/configs` (read-only)

## 🔧 Configuration

### Environment Variables
```bash
# Development flags
export DEV_MODE=true              # Enable dev features
export DEBUG_MODE=true            # Enable debug logging
export REBUILD_ON_START=true      # Auto-rebuild on start

# Telegram (optional for local testing)
export TELEGRAM_BOT_TOKEN="your_token"
export TELEGRAM_CHAT_ID="your_chat_id"
```

### Local vs Production Differences

| Feature | Local (`bot-local.sh`) | Production (`bot.sh`) |
|---------|------------------------|----------------------|
| Images | `autotrader-local-*` | `ghcr.io/hoangtrung99/autotrader-*` |
| Container names | `local-*` | Standard names |
| Auto-restart | ❌ (easier debugging) | ✅ (production stability) |
| Debug features | ✅ Enhanced | ❌ Minimal |
| Volume mounts | ✅ Development volumes | ❌ Production volumes |
| Interactive shell | ✅ `debug` command | ❌ Not available |

## 🧪 Test Suite

Script có built-in test suite:

```bash
./bot-local.sh test
```

Test suite bao gồm:
1. **Image Build Test**: Build cả 2 images
2. **Container Lifecycle Test**: Start/stop containers
3. **Telegram Bot Test**: Test Telegram bot (nếu configured)

## 🧹 Cleanup

```bash
# Cleanup tất cả local resources
./bot-local.sh cleanup

# Force cleanup không hỏi confirm
./bot-local.sh cleanup --force
```

Cleanup sẽ:
- Stop tất cả containers
- Remove tất cả containers  
- Remove local images
- Clean up dangling images

## 🚨 Troubleshooting

### Docker không khả dụng
```bash
# Check Docker service
sudo systemctl status docker

# Start Docker service  
sudo systemctl start docker

# Check connection
docker version
```

### Build image thất bại
```bash
# Check Dockerfiles exist
ls -la Dockerfile.*

# Rebuild with verbose
./bot-local.sh rebuild both

# Check logs
docker build -f Dockerfile.trader -t test .
```

### Container không start
```bash
# Check logs
./bot-local.sh logs <symbol>

# Debug mode
./bot-local.sh start <symbol> --debug

# Interactive mode (không detach)
docker run -it autotrader-local-trader:latest
```

## 💡 Tips & Best Practices

1. **Luôn check status trước**: `./bot-local.sh status`
2. **Sử dụng debug mode**: `--debug` để logs chi tiết
3. **Test với small amounts**: `--amount 1 --test-mode`
4. **Monitor logs**: `./bot-local.sh logs <symbol> 100 --follow`
5. **Clean up sau test**: `./bot-local.sh cleanup`
6. **Rebuild sau thay đổi code**: `--rebuild` option

## 🔗 Related Files

- `bot.sh` - Production deployment script
- `Dockerfile.telegram` - Telegram bot image
- `Dockerfile.trader` - Trading bot image
- `docker-compose.yml` - Production orchestration
- `DOCKER_2_IMAGE_SETUP.md` - Production setup guide

---

**Happy Local Testing!** 🚀 Script `bot-local.sh` giúp bạn develop và test AutoTrader dễ dàng mà không cần lo về production environment. 