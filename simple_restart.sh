#!/bin/bash

# Simple restart without complex output
docker stop autotrader-telegram 2>/dev/null || true
docker rm autotrader-telegram 2>/dev/null || true

# Build
docker build -t autotrader-telegram -f Dockerfile.telegram . >/dev/null 2>&1

# Run as root for Docker access
docker run -d \
    --name autotrader-telegram \
    --env-file .env \
    -v $(pwd):/app \
    -v /var/run/docker.sock:/var/run/docker.sock \
    autotrader-telegram >/dev/null 2>&1

# Check if running
if docker ps | grep -q autotrader-telegram; then
    echo "✅ Container started successfully"
else
    echo "❌ Failed to start container"
    docker logs autotrader-telegram 2>/dev/null || true
fi
