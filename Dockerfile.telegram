# Optimized Telegram Bot Dockerfile - Ultra-lightweight
FROM alpine:3.19

# Set environment variables
ENV LANG=C.UTF-8 \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1

# Install minimal system dependencies in single layer
RUN apk add --no-cache --virtual .build-deps \
    build-base \
    python3-dev \
    libffi-dev \
    openssl-dev \
    && apk add --no-cache \
    python3 \
    py3-pip \
    bash \
    curl \
    ca-certificates \
    docker-cli \
    sudo \
    && python3 -m venv /opt/venv \
    && apk del .build-deps \
    && rm -rf /var/cache/apk/* /tmp/* /root/.cache

# Use virtual environment
ENV PATH="/opt/venv/bin:$PATH"

# Create telegram user and add to docker group
RUN addgroup -g 1000 telegram && \
    adduser -D -s /bin/bash -u 1000 -G telegram telegram && \
    addgroup docker && \
    adduser telegram docker && \
    echo "telegram ALL=(ALL) NOPASSWD: /usr/bin/docker" >> /etc/sudoers

# Create minimal directory structure
RUN mkdir -p /app && \
    chown telegram:telegram /app

WORKDIR /app

# Copy only necessary files for telegram bot
COPY --chown=telegram:telegram requirements-telegram.txt ./
COPY --chown=telegram:telegram run_telegram_app.py ./
COPY --chown=telegram:telegram bot.sh ./
COPY --chown=telegram:telegram setup-container.sh ./
COPY --chown=telegram:telegram src/infrastructure/telegram/ ./src/infrastructure/telegram/

# Install minimal Python dependencies
RUN . /opt/venv/bin/activate \
    && pip install --no-cache-dir -r requirements-telegram.txt

# Set permissions and run container setup
RUN chmod +x bot.sh run_telegram_app.py setup-container.sh

# Keep as root for Docker access
# USER telegram

# Minimal volumes for runtime data only
VOLUME ["/app/data"]

# Environment for telegram bot
ENV BOT_MODE=telegram \
    LOG_LEVEL=INFO

# Health check - lightweight
HEALTHCHECK --interval=120s --timeout=10s --start-period=30s --retries=2 \
    CMD pgrep -f "run_telegram_app.py" || exit 1

# Default command
CMD python3 run_telegram_app.py start --token "${TELEGRAM_BOT_TOKEN}" --chat-id "${TELEGRAM_CHAT_ID}" 