#!/usr/bin/env python3
"""
Simple Telegram connectivity test
"""

import os
import asyncio
from telegram import Bo<PERSON>

# Get credentials
BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

async def test_telegram():
    """Test basic telegram connectivity"""
    try:
        print(f"🔍 Testing Telegram connectivity...")
        print(f"Bot Token: {BOT_TOKEN[:15]}...")
        print(f"Chat ID: {CHAT_ID}")
        
        # Create bot
        bot = Bot(token=BOT_TOKEN)
        
        # Test bot info
        print(f"📡 Getting bot info...")
        bot_info = await bot.get_me()
        print(f"✅ Bot info: @{bot_info.username} ({bot_info.first_name})")
        
        # Send test message
        print(f"📤 Sending test message...")
        message = await bot.send_message(
            chat_id=int(CHAT_ID),
            text="🧪 **Simple Connectivity Test**\n\n✅ Basic Telegram integration working!"
        )
        print(f"✅ Message sent successfully! Message ID: {message.message_id}")
        
        # Test another message with more info
        import datetime
        detailed_msg = (
            f"📊 **Detailed Test Results**\n\n"
            f"🤖 **Bot:** @{bot_info.username}\n"
            f"💬 **Chat ID:** {CHAT_ID}\n"
            f"⏰ **Time:** {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"🐍 **Python:** Working\n"
            f"📡 **API:** Connected\n\n"
            f"🎉 **Result:** All systems operational!"
        )
        
        await bot.send_message(
            chat_id=int(CHAT_ID),
            text=detailed_msg,
            parse_mode='Markdown'
        )
        print(f"✅ Detailed message sent!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    result = asyncio.run(test_telegram())
    if result:
        print(f"\n🎉 SUCCESS: Telegram integration is working!")
        print(f"📱 Check your Telegram for test messages")
    else:
        print(f"\n❌ FAILED: Telegram integration has issues") 