#!/usr/bin/env python3
"""
Wrapper script to run Central Telegram Manager with proper Python path
"""

import sys
import os
import asyncio

# Add the project root to Python path
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

# Add src directory to Python path
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# Now import and run the Central Manager
from src.infrastructure.telegram.telegram_central_manager import CentralTelegramManager, main

if __name__ == "__main__":
    asyncio.run(main()) 