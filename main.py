#!/usr/bin/env python3
"""Main entry point for the trading bot"""
import asyncio
import signal
import sys
import warnings
import contextlib
import os
import argparse
from pathlib import Path
from typing import Optional

# Disable all ccxt and exchange warnings
warnings.filterwarnings('ignore')
os.environ['PYTHONWARNINGS'] = 'ignore'

# Disable stderr for ccxt warnings
import io
original_stderr = sys.stderr

# Add src to path
sys.path.append(str(Path(__file__).parent))


@contextlib.contextmanager
def suppress_stderr():
    """Suppress stderr output temporarily"""
    stderr_backup = os.dup(2)
    with open(os.devnull, 'w') as devnull:
        os.dup2(devnull.fileno(), 2)
        try:
            yield
        finally:
            os.dup2(stderr_backup, 2)
            os.close(stderr_backup)


class TradingBotApplication:
    """Main application class"""
    
    def __init__(self, config_file: Optional[str] = None, debug: bool = False):
        # Let ConfigManager handle environment variable resolution
        from src.application.engine.trading_orchestrator import TradingOrchestrator
        self.engine = TradingOrchestrator(config_file, debug=debug)
        self.shutdown_event = asyncio.Event()
        self._shutdown_task = None
        
    def signal_handler(self, sig, frame):
        """Handle shutdown signals"""
        if not self.shutdown_event.is_set():
            print("\n🛑 Shutdown signal received. Stopping bot gracefully...")
            self.shutdown_event.set()
            # Also set the engine's shutdown event
            self.engine.shutdown_event.set()
            # Stop the engine
            self.engine.is_running = False
        
    async def run(self):
        """Run the trading bot"""
        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        try:
            print("🚀 Starting Trading Bot...")
            print(f"📊 Trading {self.engine.config.symbol}")
            print(f"📈 Direction: {self.engine.config.direction}")
            print(f"💰 Amount: ${self.engine.config.amount}")
            print(f"🔧 Mode: {'TEST' if self.engine.config.use_test_mode else 'LIVE'}")
            print("-" * 50)
            
            # Create tasks for engine and shutdown monitor
            engine_task = asyncio.create_task(self.engine.start())
            shutdown_task = asyncio.create_task(self.shutdown_event.wait())
            
            # Wait for either engine to complete or shutdown signal
            done, pending = await asyncio.wait(
                [engine_task, shutdown_task],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            # Handle shutdown gracefully
            if shutdown_task in done:
                # Shutdown signal received
                self.engine.is_running = False
                self.engine.shutdown_event.set()
                
                # Wait for engine to stop gracefully
                if engine_task not in done:
                    try:
                        await asyncio.wait_for(engine_task, timeout=10.0)
                    except asyncio.TimeoutError:
                        print("⚠️  Engine didn't stop in time, forcing shutdown...")
                        engine_task.cancel()
                        try:
                            await engine_task
                        except asyncio.CancelledError:
                            pass
            else:
                # Engine completed naturally, cancel shutdown task
                shutdown_task.cancel()
                try:
                    await shutdown_task
                except asyncio.CancelledError:
                    pass
            
        except Exception as e:
            print(f"❌ Fatal error: {e}")
            raise
        finally:
            # Ensure engine is stopped cleanly
            if hasattr(self, 'engine') and self.engine.is_running:
                try:
                    with suppress_stderr():
                        warnings.filterwarnings("ignore")
                        await self.engine.stop()
                except Exception:
                    # Silent cleanup
                    pass
            print("\n✅ Bot stopped successfully")


def print_config_summary(config_file: str):
    """Print compact config summary before starting operations"""
    try:
        from src.utils.config_printer import print_config_simple
        print("\n" + "="*60)
        print("📋 CURRENT CONFIGURATION SUMMARY")
        print("="*60)
        print_config_simple(config_file)
        print("\n" + "⏱️  Starting in 3 seconds... (Ctrl+C to cancel)")
        print("="*60 + "\n")
        
        # Small delay to let user see config
        import time
        time.sleep(3)
        
    except Exception as e:
        print(f"⚠️  Could not load config summary: {e}")
        print("Continuing anyway...\n")


def print_usage_guide():
    """Print beautiful usage guide"""
    print("""
🚀 Professional Crypto Trading Bot
==================================

📋 Usage Options:

🎯 Quick Commands:
  python main.py                    🤖 Start trading bot (default)
  python main.py --dashboard        📊 Launch CLI dashboard
  python main.py --test            🧪 Run in safe test mode
  python main.py --show-config     📋 Show pretty config details

🔧 Advanced Options:
  python main.py --config custom.json   📄 Use custom config file
  python main.py --start --test         🧪 Start in test mode
  python main.py --start --debug        🐛 Start with debug logging
  python main.py --dashboard --debug    🐛 CLI dashboard with debug logs
  python main.py --check-config         📋 Check config (same as --show-config)
  python main.py --help                 ❓ Show this help

📊 Trading Parameters (override config file):
  --symbol SYMBOL        # Trading pair (e.g., HYPER, BTC/USDT:USDT)
  --amount AMOUNT        # Position size in USDT
  --dca-amount AMOUNT    # DCA amount for all strategies
  --dca-bb-amount NUM    # DCA amount for Bollinger Bands strategy
  --dca-ema-amount NUM   # DCA amount for EMA strategies
  --exchange NAME        # Exchange name (default: bybit)
  --direction DIR        # Trade direction (LONG or SHORT)
  --max-position SIZE    # Maximum allowed position size
  --stop-loss PERCENT    # Stop loss percentage (e.g., 2 for 2%)
  --take-profit PERCENT  # Take profit percentage (e.g., 3 for 3%)
  --timeframe TF         # Primary timeframe (15m, 1h, 4h)

🔑 API Credentials (override environment variables):
  --key API_KEY          # Bybit API key
  --secret API_SECRET    # Bybit API secret

💡 Recommended Workflow:
  1. python main.py --test              # Test your strategy first
  2. python main.py --dashboard         # Monitor with CLI dashboard  

⚡ Quick Examples:
  # Basic trading with specific symbol
  python main.py --symbol HYPER --amount 100 --test

  # Full trading parameters
  python main.py --symbol BTC --amount 200 --direction LONG \\
    --dca-amount 50 --stop-loss 2 --take-profit 5 --timeframe 15m

  # With custom API credentials
  python main.py --symbol HYPER --key 'your_key' --secret 'your_secret' --test

  # Dashboard with trading parameters
  python main.py --dashboard --symbol HYPER --amount 100 --debug

⚠️  Important Notes:
  • Always test with --test flag first
  • CLI arguments override config.json settings
  • Symbol format: simple (HYPER) or full (HYPER/USDT:USDT)
  • Use Ctrl+C to stop the bot safely
  • Monitor your trades regularly

🚀 Even Faster: Use the launcher script!
  ./run.sh help                         # See all quick commands
    """)

def main():
    """Main function"""
    parser = argparse.ArgumentParser(
        description='Professional Crypto Trading Bot',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py                    # Start trading bot
  python main.py --dashboard        # CLI dashboard
  python main.py --web             # Web interface
  python main.py --test            # Safe test mode
  python main.py --show-config     # Show config details
  python main.py --symbol HYPER --amount 100  # Start with specific trading params
  
For quick access, use: ./run.sh help
        """
    )
    parser.add_argument(
        '--config',
        type=str,
        default=None,
        help='Path to configuration file (default: BOT_CONFIG_FILE env var or configs/config.json)'
    )
    parser.add_argument(
        '--test',
        action='store_true',
        help='🧪 Run in test mode without real trading'
    )
    parser.add_argument(
        '--dashboard',
        action='store_true',
        help='📊 Launch interactive CLI dashboard'
    )
    parser.add_argument(
        '--start',
        action='store_true',
        help='🤖 Start trading bot directly'
    )
    parser.add_argument(
        '--web',
        action='store_true',
        help='🌐 Launch web dashboard interface'
    )
    parser.add_argument(
        '--show-config',
        action='store_true',
        help='📋 Show detailed configuration (pretty formatted)'
    )
    parser.add_argument(
        '--check-config',
        action='store_true',
        help='📋 Check configuration (alias for --show-config)'
    )
    parser.add_argument(
        '--debug',
        action='store_true',
        help='🐛 Enable debug logging (shows detailed logs)'
    )
    
    # Trading Parameters (same as bot.sh)
    parser.add_argument(
        '--symbol',
        type=str,
        help='Trading pair (e.g., HYPER, BTC/USDT:USDT)'
    )
    parser.add_argument(
        '--amount',
        type=float,
        help='Position size in USDT'
    )
    parser.add_argument(
        '--dca-amount',
        type=float,
        help='DCA amount for all strategies'
    )
    parser.add_argument(
        '--dca-bb-amount',
        type=float,
        help='DCA amount for Bollinger Bands strategy'
    )
    parser.add_argument(
        '--dca-ema-amount',
        type=float,
        help='DCA amount for EMA strategies'
    )
    parser.add_argument(
        '--exchange',
        type=str,
        help='Exchange name (default: bybit)'
    )
    parser.add_argument(
        '--direction',
        type=str,
        choices=['LONG', 'SHORT', 'long', 'short'],
        help='Trade direction (LONG or SHORT)'
    )
    parser.add_argument(
        '--max-position',
        type=float,
        help='Maximum allowed position size'
    )
    parser.add_argument(
        '--stop-loss',
        type=float,
        help='Stop loss percentage (e.g., 2 for 2%%)'
    )
    parser.add_argument(
        '--take-profit',
        type=float,
        help='Take profit percentage (e.g., 3 for 3%%)'
    )
    parser.add_argument(
        '--timeframe',
        type=str,
        help='Primary timeframe (15m, 1h, 4h)'
    )
    parser.add_argument(
        '--key',
        type=str,
        help='Bybit API key (overrides BYBIT_API_KEY environment variable)'
    )
    parser.add_argument(
        '--secret',
        type=str,
        help='Bybit API secret (overrides BYBIT_API_SECRET environment variable)'
    )
    
    # Check if no arguments provided
    if len(sys.argv) == 1:
        # Show usage guide when running just "python main.py"
        print_usage_guide()
        return
    
    args = parser.parse_args()
    
    # Process and set trading parameters as environment variables (same as bot.sh)
    _set_trading_environment_variables(args)
    
    # Show config if requested
    if args.show_config or args.check_config:
        from src.utils.config_printer import print_config
        print_config(args.config)
        return
    
    # Set test mode from command line
    if args.test:
        import os
        os.environ['TEST_MODE'] = 'true'
    
    # Handle different launch modes
    if args.dashboard:
        # Launch CLI dashboard with debug flag
        from src.presentation.cli.cli_interface import cli
        sys.argv = ['cli', 'dashboard']
        if args.debug:
            sys.argv.append('--debug')
        cli()
        return
    elif args.start or not any([args.dashboard]):
        # Default: start trading bot
        mode = "🧪 TEST MODE" if args.test else "🤖 LIVE TRADING"
        debug_info = " (Debug Mode)" if args.debug else ""
        print(f"{mode}{debug_info}")
        
        app = TradingBotApplication(args.config, debug=args.debug)
        try:
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")  # Suppress all warnings during shutdown
                asyncio.run(app.run())
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
        except asyncio.CancelledError:
            # Normal shutdown from Ctrl+C, don't show error
            print("\n👋 Goodbye!")
        except (SystemExit, EOFError):
            # Normal exit, don't show error
            pass
        except Exception as e:
            print(f"\n💥 Application error: {e}")
            sys.exit(1)


def _set_trading_environment_variables(args):
    """Set environment variables from CLI arguments (similar to bot.sh)"""
    import os
    
    # Process symbol format (same logic as bot.sh)
    if args.symbol:
        symbol = args.symbol
        if '/' not in symbol:
            # Convert simple format to full format and uppercase
            symbol = symbol.upper()
            symbol = f"{symbol}/USDT:USDT"
        else:
            # If already in full format, ensure base symbol is uppercase
            base_part = symbol.split('/')[0]
            rest_part = '/'.join(symbol.split('/')[1:])
            base_part = base_part.upper()
            symbol = f"{base_part}/{rest_part}"
        
        os.environ['TRADE_SYMBOL'] = symbol
        print(f"📊 Symbol: {symbol}")
    
    # Set other trading parameters
    if args.amount is not None:
        os.environ['TRADE_AMOUNT'] = str(args.amount)
        print(f"💰 Amount: ${args.amount}")
    
    if args.dca_amount is not None:
        os.environ['TRADE_DCA_AMOUNT'] = str(args.dca_amount)
        print(f"📈 DCA Amount: ${args.dca_amount}")
    
    if args.dca_bb_amount is not None:
        os.environ['TRADE_DCA_BB_AMOUNT'] = str(args.dca_bb_amount)
        print(f"📊 DCA BB Amount: ${args.dca_bb_amount}")
    
    if args.dca_ema_amount is not None:
        os.environ['TRADE_DCA_EMA_AMOUNT'] = str(args.dca_ema_amount)
        print(f"📈 DCA EMA Amount: ${args.dca_ema_amount}")
    
    if args.exchange:
        os.environ['TRADE_EXCHANGE'] = args.exchange
        print(f"🏢 Exchange: {args.exchange}")
    
    if args.direction:
        direction = args.direction.upper()
        os.environ['TRADE_DIRECTION'] = direction
        print(f"📈 Direction: {direction}")
    
    if args.max_position is not None:
        os.environ['TRADE_MAX_POSITION'] = str(args.max_position)
        print(f"📊 Max Position: ${args.max_position}")
    
    if args.stop_loss is not None:
        os.environ['TRADE_STOP_LOSS'] = str(args.stop_loss)
        print(f"🛑 Stop Loss: {args.stop_loss}%")
    
    if args.take_profit is not None:
        os.environ['TRADE_TAKE_PROFIT'] = str(args.take_profit)
        print(f"🎯 Take Profit: {args.take_profit}%")
    
    if args.timeframe:
        os.environ['TRADE_TIMEFRAME'] = args.timeframe
        print(f"⏰ Timeframe: {args.timeframe}")
    
    if args.key:
        os.environ['BYBIT_API_KEY'] = args.key
        print(f"🔑 API Key: {args.key[:10]}...")
    
    if args.secret:
        os.environ['BYBIT_API_SECRET'] = args.secret
        print("🔐 API Secret: ***")
    
    # Set test mode environment variable
    if hasattr(args, 'test') and args.test:
        os.environ['TRADE_TEST_MODE'] = 'true'
        print("🧪 Test Mode: Enabled")


if __name__ == '__main__':
    main() 