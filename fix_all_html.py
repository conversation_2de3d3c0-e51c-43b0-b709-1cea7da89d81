#!/usr/bin/env python3
"""Fix all HTML formatting to Mark<PERSON> in the improved_command_handler.py file."""

import re

def fix_html_to_markdown():
    """Fix HTML to Markdown in the file."""
    file_path = 'src/infrastructure/telegram/handlers/improved_command_handler.py'
    
    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Replace HTML tags with Markdown
    content = re.sub(r'<b>([^<]+)</b>', r'**\1**', content)
    content = re.sub(r'<i>([^<]+)</i>', r'*\1*', content)
    content = re.sub(r'<code>([^<]+)</code>', r'`\1`', content)
    content = re.sub(r'<pre>([^<]+)</pre>', r'```\n\1\n```', content)
    
    # Replace HTML entities
    content = re.sub(r'&lt;', '<', content)
    content = re.sub(r'&gt;', '>', content)
    content = re.sub(r'&amp;', '&', content)
    
    # Replace ParseMode.HTML with ParseMode.MARKDOWN
    content = re.sub(r'ParseMode\.HTML', 'ParseMode.MARKDOWN', content)
    
    # Replace escape_html calls
    content = re.sub(r'self\.escape_html\(([^)]+)\)', r'\1', content)
    
    # Replace user.mention_html() with user.first_name
    content = re.sub(r'{user\.mention_html\(\)}', '{user.first_name}', content)
    
    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Fixed all HTML formatting to Markdown")

if __name__ == "__main__":
    fix_html_to_markdown()
