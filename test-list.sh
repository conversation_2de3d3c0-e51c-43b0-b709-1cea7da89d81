#!/bin/bash

# Test list_containers function

# Copy check_docker function from bot.sh
check_docker() {
    echo "🔍 Debug: Starting check_docker function"
    
    # If running inside Docker container, assume Docker is available via socket
    if [ -f /.dockerenv ]; then
        echo "🐳 Running inside Docker container"
        # Always use sudo in container since docker socket permissions are complex
        export DOCKER_CMD="sudo docker"
        echo "🔧 Set DOCKER_CMD to: $DOCKER_CMD"
        
        # Test the docker command
        echo "🧪 Testing Docker command..."
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        echo "✅ Docker command works!"
        return 0
    fi

    # Check if Docker socket is available
    if [ -S /var/run/docker.sock ]; then
        export DOCKER_CMD="docker"
        if ! $DOCKER_CMD version >/dev/null 2>&1; then
            echo "❌ Cannot connect to Docker daemon."
            return 1
        fi
        return 0
    fi

    if ! command -v docker >/dev/null 2>&1; then
        echo "❌ Docker not found. Please install Docker."
        return 1
    fi

    export DOCKER_CMD="docker"
    if ! $DOCKER_CMD version >/dev/null 2>&1; then
        echo "❌ Cannot connect to Docker daemon."
        return 1
    fi

    return 0
}

# Copy list_containers function from bot.sh
list_containers() {
    echo "📊 Trading Bot Containers"
    echo "========================="
    
    if ! check_docker; then
        return 1
    fi
    
    echo "🔧 Using DOCKER_CMD: $DOCKER_CMD"
    
    local containers=$($DOCKER_CMD ps -a --format "table {{.Names}}\t{{.Status}}\t{{.Image}}\t{{.CreatedAt}}" 2>/dev/null | grep -E "(autotrader|crypto|trading)" || true)
    
    if [[ -z "$containers" ]]; then
        echo "📭 No trading bot containers found"
        echo ""
        echo "💡 Start a bot with:"
        echo "  ./bot.sh start <symbol>"
        return 0
    fi
    
    echo "$containers"
}

echo "🧪 Testing list_containers function..."
list_containers
