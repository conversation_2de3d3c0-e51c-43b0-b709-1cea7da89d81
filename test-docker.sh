#!/bin/bash

# Test Docker functionality in container

echo "🧪 Testing Docker functionality..."

# Check if running in container
if [ -f /.dockerenv ]; then
    echo "✅ Running inside Docker container"
    
    # Set Docker command
    if [ "$(id -u)" != "0" ]; then
        DOCKER_CMD="sudo docker"
    else
        DOCKER_CMD="docker"
    fi
    
    echo "🐳 Using Docker command: $DOCKER_CMD"
    
    # Test Docker version
    echo "📋 Docker version:"
    $DOCKER_CMD version
    
    echo ""
    echo "📊 Docker containers:"
    $DOCKER_CMD ps --format "table {{.Names}}\t{{.Status}}\t{{.Image}}"
    
else
    echo "❌ Not running in Docker container"
fi
