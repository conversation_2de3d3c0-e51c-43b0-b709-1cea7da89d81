# 🧪 Test Telegram Central Manager Local

Hướng dẫn test tính năng Telegram mới ở local environment.

## 📋 Prerequisites Check

### 1. **Kiểm tra Dependencies**

```bash
# Kiểm tra python-telegram-bot
python3 -c "import telegram; print('✅ python-telegram-bot OK')"

# Kiểm tra Docker
docker --version
docker info

# Kiểm tra Docker SDK
python3 -c "import docker; print('✅ Docker SDK OK')"
```

### 2. **Tạo Telegram Bot** (nếu chưa có)

1. Mở Telegram → Tìm `@BotFather`
2. G<PERSON>i `/newbot`
3. Đặt tên: `Trading Test Bot`
4. Đặt username: `trading_test_local_bot`
5. Copy token nhận được

### 3. **Lấy Chat ID**

1. Gửi tin nhắn cho bot vừa tạo
2. Truy cập: `https://api.telegram.org/bot<YOUR_TOKEN>/getUpdates`
3. Copy `chat.id` từ response

## ⚙️ Setup Local Environment

### 1. **Tạ<PERSON> thư mục cần thiết**

```bash
# Tạo folder cho Telegram credentials
mkdir -p data/telegram_credentials

# Tạo folder cho Docker volumes
mkdir -p docker/data docker/logs
```

### 2. **Set Environment Variables**

```bash
# Thêm vào terminal session hiện tại
export TELEGRAM_BOT_TOKEN='your_bot_token_here'
export TELEGRAM_CHAT_ID='your_chat_id_here'

# Verify
echo "Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
echo "Chat ID: $TELEGRAM_CHAT_ID"
```

### 3. **Hoặc tạo file .env**

```bash
cat > .env << EOF
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_CHAT_ID=your_chat_id_here
EOF
```

## 🚀 Test Central Manager

### 1. **Test khởi động**

```bash
# Kiểm tra script
./telegram_central.sh status

# Start central manager
./telegram_central.sh start
```

### 2. **Kiểm tra logs**

```bash
# Xem logs real-time
tail -f telegram_central.log

# Hoặc dùng script
./telegram_central.sh logs
```

### 3. **Test commands trong Telegram**

Gửi trong Telegram chat:

```
/help
/status
/list
```

## 🤖 Test với Trading Bots

### 1. **Tạo test configuration**

```bash
# Copy config mẫu
cp configs/config.json configs/test_local.json

# Edit config cho test
# Thay đổi symbol, amount nhỏ để test
```

### 2. **Khởi động test bot**

```bash
# Start một bot test với amount nhỏ
./bot.sh start hyper --amount 10 --config test_local.json --test-mode

# Kiểm tra container được tạo
docker ps
```

### 3. **Test monitoring qua Telegram**

```
/list                    # Xem containers
/status hyper_trader_1   # Status chi tiết
/logs hyper_trader_1     # Xem logs
```

## 🧪 Test các tính năng mới

### 1. **Test Credential Management**

```
/addcreds               # Thêm API credentials
/listcreds              # Xem credentials hiện có
```

### 2. **Test Bot Creation Wizard**

```
/createbot              # Khởi động wizard 7 bước
```

### 3. **Test Bulk Operations**

```
/bulkcreate             # Tạo nhiều bots
/bulkstatus             # Status tất cả bots
/bulklogs               # Logs từ nhiều bots
```

### 4. **Test Config Management**

```
/createconfig           # Tạo config mới
/listconfigs            # Xem configs
/editconfig             # Chỉnh sửa config
```

## 🐛 Troubleshooting

### **Lỗi thường gặp:**

1. **"Bot credentials not found"**

   ```bash
   # Kiểm tra env vars
   env | grep TELEGRAM
   ```

2. **"Docker daemon not running"**

   ```bash
   # Start Docker
   sudo systemctl start docker  # Linux
   # Hoặc start Docker Desktop    # macOS/Windows
   ```

3. **"Module 'telegram' not found"**

   ```bash
   # Reinstall dependencies
   pip install python-telegram-bot==20.7
   ```

4. **"Permission denied" khi tạo folders**
   ```bash
   # Fix permissions
   sudo chown -R $USER:$USER data/ docker/
   ```

### **Debug commands:**

```bash
# Kiểm tra process
ps aux | grep telegram

# Kiểm tra ports
netstat -tulpn | grep telegram

# Kill process nếu cần
pkill -f telegram_central_manager
```

## ✅ Test Checklist

- [ ] Dependencies installed
- [ ] Bot token & chat ID configured
- [ ] Central manager starts successfully
- [ ] `/help` command works in Telegram
- [ ] Can list containers with `/list`
- [ ] Can view status with `/status`
- [ ] Can view logs with `/logs`
- [ ] Can create credentials with `/addcreds`
- [ ] Bot creation wizard works with `/createbot`
- [ ] Bulk operations work with `/bulkcreate`
- [ ] Can stop containers via Telegram
- [ ] Notifications work properly

## 🎯 Next Steps

Sau khi test local thành công:

1. **Production deployment:**

   ```bash
   # Build Docker image
   docker build -t trading-bot-v2 .

   # Deploy với docker-compose
   docker-compose up -d
   ```

2. **Monitor production:**

   ```bash
   # View production logs
   docker-compose logs -f trading-bot
   ```

3. **Backup configurations:**
   ```bash
   # Backup important data
   tar -czf backup_$(date +%Y%m%d).tar.gz data/ configs/
   ```
