#!/usr/bin/env python3
"""
Test script to send commands to Telegram bot
"""

import asyncio
import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def test_commands():
    """Test various Telegram bot commands"""
    
    # Get credentials from environment
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if not token or not chat_id:
        print("❌ Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID")
        return
    
    client = TelegramAPIClient(token)
    
    print("🧪 Testing Telegram Bot Commands")
    print("=" * 40)
    
    # Test commands
    commands_to_test = [
        "/help",
        "/listconfigs",
        "/showconfig config",
        "/subscribe",
        "/testnotify trade",
        "/testnotify position",
        "/testnotify error"
    ]
    
    for command in commands_to_test:
        print(f"\n📤 Sending: {command}")
        try:
            await client.send_message(chat_id, command)
            print("✅ Sent successfully")
            # Wait a bit between commands
            await asyncio.sleep(2)
        except Exception as e:
            print(f"❌ Error sending {command}: {e}")
    
    print("\n✅ Test completed. Check your Telegram chat for responses.")

if __name__ == "__main__":
    asyncio.run(test_commands())
