#!/usr/bin/env python3
"""Convert HTML formatting to Markdown in Telegram bot files."""

import re
import os

def convert_html_to_markdown(content):
    """Convert HTML tags to Markdown format."""
    # Replace HTML tags with Markdown equivalents
    content = re.sub(r'<b>([^<]+)</b>', r'**\1**', content)
    content = re.sub(r'<i>([^<]+)</i>', r'*\1*', content)
    content = re.sub(r'<code>([^<]+)</code>', r'`\1`', content)
    content = re.sub(r'<pre>([^<]+)</pre>', r'```\n\1\n```', content)

    # Replace ParseMode.HTML with ParseMode.MARKDOWN
    content = re.sub(r'ParseMode\.HTML', 'ParseMode.MARKDOWN', content)
    content = re.sub(r'parse_mode=ParseMode\.HTML', 'parse_mode=ParseMode.MARKDOWN', content)
    content = re.sub(r'"HTML"', '"Markdown"', content)
    content = re.sub(r'parse_mode="HTML"', 'parse_mode="Markdown"', content)

    # Replace user.mention_html() with user.first_name
    content = re.sub(r'{user\.mention_html\(\)}', r'{user.first_name}', content)

    # Replace escape_html calls with direct text
    content = re.sub(r'self\.escape_html\(([^)]+)\)', r'\1', content)

    return content

def main():
    """Main function to convert files."""
    file_path = 'src/infrastructure/telegram/handlers/improved_command_handler.py'

    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return

    # Read the file
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()

    # Convert HTML to Markdown
    converted_content = convert_html_to_markdown(content)

    # Write back to file
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(converted_content)

    print(f"✅ Converted {file_path} from HTML to Markdown")

if __name__ == "__main__":
    main()
