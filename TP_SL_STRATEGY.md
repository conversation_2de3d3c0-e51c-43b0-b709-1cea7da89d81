# 📊 Chiến lược TP/SL đã tối ưu cho Trading Bot

## 🎯 Tổng quan cải tiến

### 1. **Tự động tạo TP/SL ngay khi mở position**

- <PERSON><PERSON> thêm method `create_tp_sl_orders()` trong `ExchangePositionManager`
- TP/SL orders được tạo tự động sau khi position được mở thành công
- Không cần chờ đến loop tiếp theo

### 2. **Tính toán TP/SL động dựa trên volatility**

- Sử dụng Bollinger Band width làm thước đo volatility
- Công thức tính:

  ```
  volatility_factor = BB_width / entry_price

  TP_distance = entry_price * (0.01 + volatility_factor * 0.5)
  SL_distance = entry_price * (0.005 + volatility_factor * 0.25)
  ```

- Volatility cao → TP/SL xa hơn
- Vẫn giữ giới hạn min/max từ config

### 3. **Điều chỉnh TP/SL sau DCA**

- Thêm method `_adjust_tp_sl_after_dca()`
- Tính lại average entry price sau mỗi DCA fill
- Điều chỉnh TP/SL dựa trên giá trung bình mới
- Tự động hủy orders cũ và tạo orders mới

### 4. **Cấu hình linh hoạt**

Thêm các options mới trong RiskConfig:

- `tp_sl_strategy`: Chọn chiến lược ('fixed', 'volatility_based')
- `tp_sl_immediately`: Tạo TP/SL ngay lập tức (default: true)
- `adjust_tp_sl_on_dca`: Điều chỉnh TP/SL sau DCA (default: true)
- `partial_take_profit`: Chốt lời từng phần (future feature)

## 📋 Workflow mới

### Khi mở position:

1. `_execute_loop_iteration()` → `handle_no_position_planned_pair()`
2. Tạo signal với TP/SL được tính động
3. Tạo entry order
4. Tạo position metadata
5. **Tự động gọi `create_tp_sl_orders()`** ✨
6. TP và SL orders được đặt ngay lập tức

### Khi có DCA:

1. DCA order được tạo ở mức giá định sẵn
2. Khi DCA fill, gọi `_adjust_tp_sl_after_dca()`
3. Tính lại average price
4. Hủy TP/SL cũ, tạo TP/SL mới
5. Update metadata

### Khi quản lý position:

1. Update PnL liên tục
2. Kiểm tra trailing stop (nếu enabled)
3. Kiểm tra điều kiện đóng lệnh khẩn cấp

## 🔧 Cấu hình mẫu

```json
{
  "risk": {
    "default_stop_loss": 2.0,
    "default_take_profit": 3.0,
    "tp_sl_strategy": "volatility_based",
    "tp_sl_immediately": true,
    "adjust_tp_sl_on_dca": true,
    "use_trailing_stop": false,
    "trailing_stop_distance": 1.0
  },
  "planned_pair": {
    "long": {
      "take_profit_multiplier": 1.005,
      "stop_loss_multiplier": 0.995
    },
    "short": {
      "take_profit_multiplier": 0.995,
      "stop_loss_multiplier": 1.005
    }
  }
}
```

## 📈 Ưu điểm của chiến lược mới

1. **Bảo vệ vốn tốt hơn**: TP/SL được đặt ngay lập tức
2. **Thích ứng với thị trường**: TP/SL điều chỉnh theo volatility
3. **Tối ưu hóa DCA**: TP/SL được cập nhật sau mỗi DCA
4. **Linh hoạt**: Dễ dàng thay đổi chiến lược qua config

## 🚀 Các cải tiến trong tương lai

1. **ATR-based TP/SL**: Khi thêm ATR vào TechnicalIndicators
2. **Support/Resistance levels**: Đặt TP/SL ở các mức kỹ thuật
3. **Partial take profit**: Chốt lời từng phần ở nhiều mức
4. **Time-based adjustments**: Điều chỉnh TP/SL theo thời gian giữ position
5. **Risk/Reward optimization**: Tự động điều chỉnh để đạt R:R tối ưu

## ⚠️ Lưu ý quan trọng

1. **Test kỹ trên testnet** trước khi chạy real money
2. **Monitor orders**: Đảm bảo TP/SL orders được tạo thành công
3. **Check exchange limits**: Một số sàn giới hạn số lượng orders
4. **Slippage**: TP/SL có thể không fill đúng giá trong điều kiện volatility cao
