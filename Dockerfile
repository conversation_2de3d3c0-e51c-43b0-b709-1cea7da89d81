# Multi-stage Dockerfile for Professional Trading Bot
# Stage 1: Base dependencies and requirements
FROM python:3.11-slim as base

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for security
RUN groupadd -r trader && useradd -r -g trader trader

# Stage 2: Dependencies installation
FROM base as dependencies

WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Stage 3: Production image
FROM dependencies as production

# Create necessary directories
RUN mkdir -p /app/configs /app/data /app/logs /app/src

# Copy application code
COPY . /app/

# Set proper permissions
RUN chown -R trader:trader /app && \
    chmod +x /app/main.py

# Switch to non-root user
USER trader

# Create volume mount points
VOLUME ["/app/configs", "/app/data", "/app/logs"]

# Default working directory
WORKDIR /app

# Stage 4: Development image with additional tools
FROM production as development

# Switch to root to install dev dependencies
USER root

# Install development dependencies
RUN pip install --no-cache-dir \
    pytest \
    pytest-asyncio \
    black \
    flake8 \
    mypy

# Switch back to trader user
USER trader

# Default command for all stages
CMD ["python", "main.py"] 