# 🤖 Hướng dẫn Setup Telegram Central Manager v2.0

Hướng dẫn này giúp bạn setup **Central Telegram Manager v2.0** - <PERSON><PERSON> thống quản lý trading bot toàn diện với kiến trúc modular và tính năng nâng cao.

## 📋 Mục lục

1. [T<PERSON>h năng mới](#tính-năng-mới)
2. [Kiến trúc hệ thống](#kiến-trúc-hệ-thống)
3. [Tạo Telegram Bot](#tạo-telegram-bot)
4. [C<PERSON><PERSON> hình](#cấu-hình)
5. [Khởi động hệ thống](#khởi-động-hệ-thống)
6. [<PERSON><PERSON><PERSON> lệnh Telegram](#các-lệnh-telegram)
7. [Tính năng nâng cao](#tính-năng-nâng-cao)
8. [Troubleshooting](#troubleshooting)

## 🚀 Tính năng mới

### 🎯 Tính năng cơ bản

- ✅ Quản lý tập trung tất cả trading containers
- ✅ Nhận thông báo real-time về container status
- ✅ Xem logs, stop/start containers từ xa
- ✅ Monitor CPU, memory usage
- ✅ Tự động detect và theo dõi trading containers
- ✅ Không bị conflict khi nhiều containers

### 🆕 Tính năng mới v2.0

- 🔐 **Quản lý Credentials** - Setup API keys qua Telegram
- 🎛️ **Config Management** - Tạo/chỉnh sửa config files
- 🧙 **Interactive Wizards** - Setup bot với 7-step wizard
- 📡 **Real-time Events** - Monitor TP/SL/Orders từ exchange
- 📦 **Bulk Operations** - Quản lý nhiều bots cùng lúc
- 🛡️ **Security Enhanced** - Input validation & sanitization
- 📊 **Performance Tracking** - P&L reports & statistics
- 🔄 **Auto-recovery** - Smart error handling
- 🧪 **Testing Framework** - Comprehensive system testing

## 🏗️ Kiến trúc hệ thống

### 📦 Modules chính

```
src/infrastructure/telegram/
├── telegram_central_manager.py     # Central coordinator
├── telegram_base.py               # Shared utilities
├──
├── managers/                      # Core managers
│   ├── telegram_credentials_manager.py
│   ├── telegram_bot_manager.py
│   ├── telegram_config_manager.py
│   ├── telegram_notification_manager.py
│   └── telegram_wizard_handler.py
│
├── handlers/                      # Specialized handlers
│   ├── bot_commands_handler.py
│   ├── bot_operations_handler.py
│   ├── bot_wizards_handler.py
│   └── bulk_operations_handler.py
│
├── events/                        # Real-time monitoring
│   ├── trading_event_listener.py
│   ├── exchange_event_handler.py
│   └── telegram_event_integration.py
│
├── validators/                    # Input validation
│   └── telegram_validators.py
│
└── testing/                       # Testing framework
    └── telegram_system_test.py
```

### 🔄 Workflow

1. **Central Manager** nhận commands từ Telegram
2. **Validators** kiểm tra input security
3. **Handlers** xử lý logic specific
4. **Managers** thực hiện operations
5. **Event System** monitor real-time
6. **Notifications** gửi feedback cho user

## 🔧 Tạo Telegram Bot

### Bước 1: Tạo Bot với BotFather

1. **Mở Telegram** và tìm `@BotFather`

2. **Gửi lệnh** `/newbot` để tạo bot mới

3. **Đặt tên bot** (ví dụ: "Trading Bot Central Manager v2.0")

4. **Đặt username** cho bot (phải kết thúc bằng "bot", ví dụ: "trading_central_v2_bot")

5. **Copy TOKEN** mà BotFather cung cấp:
   ```
   **********************************************
   ```

### Bước 2: Lấy Chat ID

1. **Gửi tin nhắn** bất kỳ cho bot của bạn trong Telegram

2. **Truy cập URL** sau (thay YOUR_BOT_TOKEN):

   ```
   https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates
   ```

3. **Tìm "chat"** trong response và copy `id`:
   ```json
   {
     "chat": {
       "id": 123456789,
       "first_name": "Your Name"
     }
   }
   ```

## ⚙️ Cấu hình

### Environment Variables (Khuyến nghị)

```bash
# Thêm vào ~/.bashrc hoặc ~/.zshrc
export TELEGRAM_BOT_TOKEN="**********************************************"
export TELEGRAM_CHAT_ID="123456789"

# Reload shell
source ~/.bashrc  # hoặc source ~/.zshrc
```

### File .env

Tạo file `.env` trong thư mục project:

```bash
# Telegram Central Manager Configuration
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=123456789
```

## 🎛️ Central Telegram Manager

Central Manager quản lý tất cả trading containers từ một Telegram bot duy nhất.

### Cài đặt Dependencies

```bash
# Central Manager requires Docker SDK
pip install docker==7.0.0
```

### Khởi động Central Manager

```bash
# Start central manager
./telegram_central.sh start

# Check status
./telegram_central.sh status

# View logs
./telegram_central.sh logs

# Stop
./telegram_central.sh stop
```

### Auto-detection Trading Containers

Central Manager tự động phát hiện và theo dõi các trading containers có:

- Labels: `app=crypto-trading-bot`
- Container names: prefix với trading symbols (btc, eth, hyper, etc.)

## 🚀 Chạy Bot

### 1. Khởi động Central Manager

```bash
# Export credentials
export TELEGRAM_BOT_TOKEN="your_token"
export TELEGRAM_CHAT_ID="your_chat_id"

# Start central manager
./telegram_central.sh start
```

### 2. Khởi động Trading Bots

```bash
# Start multiple trading bots (no embedded telegram)
./bot.sh start btc --amount 100
./bot.sh start eth --amount 200
./bot.sh start hyper --amount 50

# Central Manager sẽ tự động detect và monitor các containers
```

### 3. Sử dụng qua Telegram

Mở Telegram chat với bot và sử dụng các lệnh quản lý.

## 📱 Các lệnh Telegram

Gửi các lệnh này trong chat với Central Manager bot:

### 🎯 Lệnh cơ bản

- `/start` - Khởi động bot và xem main menu
- `/help` - Hướng dẫn sử dụng chi tiết
- `/menu` - Hiển thị interactive menu

### 🤖 Quản lý Containers

- `/list` - Liệt kê tất cả trading containers
- `/status` - Tổng quan toàn bộ hệ thống
- `/status btc` - Chi tiết container BTC
- `/logs btc` - Xem logs container BTC (100 dòng cuối)
- `/stop btc` - Dừng container BTC
- `/stopall` - Dừng tất cả containers
- `/restart btc` - Restart container BTC

### 🔐 Quản lý Credentials

- `/listcreds` - Liệt kê credentials đã lưu
- `/addcreds` - Thêm API credentials mới
- `/editcreds` - Chỉnh sửa credentials
- `/deletecreds` - Xóa credentials
- `/setkey` - Quick setup API key

### ⚙️ Quản lý Configs

- `/listconfigs` - Liệt kê config files
- `/createconfig` - Tạo config file mới
- `/editconfig` - Chỉnh sửa config
- `/deleteconfig` - Xóa config file
- `/configtemplate` - Sử dụng template

### 🧙 Bot Creation Wizards

- `/createbot` - Wizard tạo bot mới (7 steps)
- `/quickbot` - Quick bot creation
- `/startbot` - Start bot với parameters

### 📦 Bulk Operations

- `/bulkcreate` - Tạo nhiều bots cùng lúc
- `/bulkdeploy` - Deploy multiple strategies
- `/bulkstatus` - Status overview tất cả bots
- `/bulklogs` - Aggregate logs từ nhiều bots

### 📡 Real-time Monitoring

- `/eventstart` - Bật real-time event monitoring
- `/eventstop` - Tắt event monitoring
- `/eventstatus` - Status của event system
- `/eventconfig` - Cấu hình event filters
- `/eventfilters` - Quản lý filters

### 📊 Reports & Analytics

- `/performance` - Xem performance metrics
- `/profits` - P&L reports
- `/dailyreport` - Daily trading summary
- `/statistics` - Trading statistics

### 🛠️ System Tools

- `/health` - System health check
- `/test` - Run system diagnostics
- `/backup` - Backup configurations
- `/restore` - Restore from backup

### Thông báo tự động

Central Manager v2.0 tự động thông báo:

**📦 Container Events:**

- ✅ Container mới được tạo
- ⚠️ Container stopped/exited
- 🔄 Container restarted
- 📊 High CPU/Memory usage (>80%)

**📡 Trading Events (Real-time):**

- 🎯 Take Profit triggered
- 🛑 Stop Loss triggered
- 📈 Order filled
- ⚡ Position opened/closed
- 💰 P&L milestones
- ⚠️ Risk alerts

**🔔 System Alerts:**

- 🔴 Exchange connection issues
- ⚠️ API rate limits
- 💾 Low disk space
- 🔋 High resource usage

## 🆕 Tính năng nâng cao

### 🧙 Interactive Bot Creation Wizard

Sử dụng `/createbot` để khởi động wizard 7-step:

```
Step 1: Bot Name           → my_btc_bot
Step 2: Trading Symbol     → BTC/USDT:USDT
Step 3: Trading Amount     → $500
Step 4: API Credentials    → Select from saved creds
Step 5: Risk Management    → 2% SL, 5% TP, 10x leverage
Step 6: Strategy Config    → DCA with EMA signals
Step 7: Final Review       → Confirm and create
```

### 📡 Real-time Event Monitoring

**Bật monitoring:**

```
/eventstart
→ ✅ Event monitoring started
→ 🔗 Connected to Bybit WebSocket
→ 📊 Monitoring 3 active bots
```

**Cấu hình filters:**

```
/eventfilters
→ 🎯 TP/SL Events: ✅ Enabled
→ 📈 Order Events: ✅ Enabled
→ 💰 P&L Alerts: > $100
→ ⚠️ Risk Alerts: > 50% drawdown
```

### 📦 Bulk Operations

**Quick setup 5 bots:**

```
/bulkcreate
→ 📊 Quick Setup (3 Bots)
→ 🚀 Standard Setup (5 Bots) ✅ Selected
→ ⚙️ Custom Setup

Creating BTC, ETH, BNB, HYPER, SOL bots...
Progress: ▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓▓ 100%
✅ 5/5 bots created successfully
```

**Bulk status overview:**

```
/bulkstatus
📊 Bulk Status Overview

Total Containers: 8
🟢 Running: 6
🔴 Stopped: 2
🟡 Other: 0

🟢 Running Containers:
• btc_trader_1 - 2h 15m
• eth_trader_1 - 1h 45m
• hyper_trader_1 - 30m
```

### 🔐 Credentials Management

**Setup với wizard:**

```
/addcreds
→ Select Exchange: Bybit ✅
→ API Key: [Enter your key]
→ API Secret: [Enter your secret]
→ Testnet: No ✅
→ Display Name: "Main Bybit Account"
→ ✅ Credentials saved securely
```

**Quick key setup:**

```
/setkey
→ Paste your Bybit API key:
→ 1234567890abcdef... ✅
→ Paste your API secret:
→ abcdef1234567890... ✅
→ ✅ Credentials configured
```

### ⚙️ Configuration Templates

**Sử dụng template:**

```
/configtemplate
→ 📊 Scalping Template
→ 💰 DCA Template ✅ Selected
→ 🎯 Swing Trading Template

DCA Template applied:
• Timeframe: 1h
• DCA Steps: 5
• Stop Loss: 2%
• Take Profit: 5%
• Strategy: EMA + BB
```

### 📊 Performance Reports

**Daily P&L report:**

```
/dailyreport
📊 Daily Trading Report - Dec 15, 2024

💰 Total P&L: +$245.67 (+4.91%)
📈 Winning Trades: 8/12 (66.7%)
🎯 Best Performer: BTC_trader (+$120)
⚠️ Worst Performer: HYPER_trader (-$25)

Top Signals:
• TP Triggered: 5 times
• SL Triggered: 2 times
• Max Drawdown: 1.2%
```

### 🧪 System Testing

**Run comprehensive tests:**

```
/test
🧪 Running System Diagnostics...

✅ Modules: 8/8 passed
✅ Integrations: 5/5 passed
✅ Performance: Response time 0.145s
✅ Security: 6/6 tests passed
🎉 All systems operational
```

## 🔧 Troubleshooting

### 🚀 Quick Diagnostics

**Run system health check:**

```bash
# Use built-in diagnostics
/health
/test

# Check all modules status
/modulestatus
```

### 🐛 Common Issues

#### Central Manager không phát hiện containers

```bash
# Check Docker labels
docker inspect <container_name> | grep -A5 Labels

# Containers phải có labels:
"Labels": {
    "app": "crypto-trading-bot",
    "symbol": "BTC"
}
```

#### Telegram bot không phản hồi

```bash
# Check credentials
echo $TELEGRAM_BOT_TOKEN
echo $TELEGRAM_CHAT_ID

# Test bot connection
curl -X GET "https://api.telegram.org/bot$TELEGRAM_BOT_TOKEN/getMe"

# Check central manager logs
./telegram_central.sh logs

# Test specific modules
/test validators
/test notifications
```

#### Event monitoring không hoạt động

```bash
# Check WebSocket connections
/eventstatus

# Restart event system
/eventstop
/eventstart

# Check exchange connectivity
/health exchange
```

#### Bulk operations thất bại

```bash
# Check operation status
/bulkstatus

# Verify Docker permissions
docker ps

# Check resource usage
/health system
```

#### Wizard bị stuck

```bash
# Reset wizard session
/cancel
/resetwizard

# Clear user session
/clearsession

# Start fresh wizard
/createbot
```

### 🔒 Security Issues

#### Credentials không save được

```bash
# Check encryption status
/health security

# Verify permissions
ls -la ~/.telegram_central/

# Regenerate encryption keys
/regeneratekeys
```

#### Validation errors

```bash
# Test validators
/test validators

# Check input sanitization
/test security

# Update validation rules
/updatevalidators
```

### 🔧 System Maintenance

#### Permission errors với Docker

```bash
# Add user to docker group
sudo usermod -aG docker $USER

# Logout and login again
# Or run with sudo (not recommended)
sudo ./telegram_central.sh start
```

#### Central Manager crash

```bash
# Check system requirements
docker --version  # Docker required
python3 --version  # Python 3.8+ required
pip install -r requirements.txt  # Install all dependencies

# Check logs for detailed error
./telegram_central.sh logs

# Run comprehensive tests
/test all

# Restart with debug mode
DEBUG=1 ./telegram_central.sh start
```

#### Module import errors

```bash
# Check Python path
echo $PYTHONPATH

# Install missing dependencies
pip install -r requirements.txt

# Verify module structure
ls -la src/infrastructure/telegram/

# Test module imports
python3 -c "from src.infrastructure.telegram.telegram_central_manager import *"
```

### 📊 Performance Issues

#### Slow response times

```bash
# Check performance metrics
/performance

# Monitor resource usage
/health system

# Optimize configurations
/optimize

# Clean up old sessions
/cleanup
```

#### Memory leaks

```bash
# Monitor memory usage
docker stats telegram_central

# Restart services
./telegram_central.sh restart

# Check for resource leaks
/health memory
```

### 🆘 Emergency Procedures

#### Complete system reset

```bash
# Stop all services
/stopall

# Backup configurations
/backup

# Reset to factory defaults
./telegram_central.sh reset

# Restore from backup
/restore
```

#### Recovery from backup

```bash
# List available backups
/listbackups

# Restore specific backup
/restore 2024-12-15_backup

# Verify system integrity
/test all
```

## 🚨 Lưu ý bảo mật

- ✅ **Không share** Telegram Bot Token
- ✅ **Chỉ add** trusted users vào chat
- ✅ **Sử dụng** private chat, không group chat
- ✅ **Monitor** Central Manager logs thường xuyên
- ✅ **Backup** credentials và configurations

## 📚 Ví dụ hoàn chỉnh

```bash
# 1. Setup credentials
export TELEGRAM_BOT_TOKEN="your_token_here"
export TELEGRAM_CHAT_ID="your_chat_id_here"

# 2. Start central manager
./telegram_central.sh start

# 3. Start multiple trading bots
./bot.sh start btc --amount 100 --test-mode
./bot.sh start eth --amount 200 --direction LONG
./bot.sh start hyper --amount 50 --dca-amount 20

# 4. Monitor via Telegram
# - Gửi /list để xem all containers
# - Gửi /status để xem overview
# - Gửi /status btc để xem chi tiết BTC
# - Gửi /logs btc để xem logs
# - Gửi /stop btc để dừng BTC bot
```

**🎉 Chúc bạn trading thành công!**
