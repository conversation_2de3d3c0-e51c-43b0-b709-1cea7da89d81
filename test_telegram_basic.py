#!/usr/bin/env python3
"""
Basic Telegram Bot Test Script
"""

import os
import asyncio
import logging
from telegram import Bo<PERSON>, Update
from telegram.ext import Application, CommandHandler, ContextTypes

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get credentials from environment
BOT_TOKEN = os.getenv('TELEGRAM_BOT_TOKEN')
CHAT_ID = os.getenv('TELEGRAM_CHAT_ID')

class BasicTelegramBot:
    def __init__(self):
        if not BOT_TOKEN or not CHAT_ID:
            raise ValueError("TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID must be set")
        
        self.bot_token = BOT_TOKEN
        self.chat_id = int(CHAT_ID)
        self.application = None
        self.bot = None
        
        logger.info(f"Bot initialized with token: {BOT_TOKEN[:15]}...")
        logger.info(f"Chat ID: {CHAT_ID}")

    async def start(self):
        """Start the bot"""
        try:
            logger.info("🤖 Starting Basic Telegram Bot...")
            
            # Create application
            self.application = Application.builder().token(self.bot_token).build()
            self.bot = self.application.bot
            
            # Add handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(CommandHandler("help", self.help_command))
            self.application.add_handler(CommandHandler("test", self.test_command))
            self.application.add_handler(CommandHandler("ping", self.ping_command))
            
            # Initialize and start
            await self.application.initialize()
            await self.application.start()
            
            # Send startup message
            await self.send_message(
                "🎉 **Basic Telegram Bot Started!**\n\n"
                "📋 **Available Commands:**\n"
                "• `/start` - Show welcome\n"
                "• `/help` - Show help\n"
                "• `/test` - Test message\n"
                "• `/ping` - Check connectivity\n\n"
                "✅ Bot is ready for testing!"
            )
            
            logger.info("✅ Bot started successfully")
            
            # Start polling
            await self.application.run_polling(stop_signals=None)
            
        except Exception as e:
            logger.error(f"❌ Failed to start bot: {e}")
            raise

    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        await update.message.reply_text(
            "🎉 **Welcome to Basic Telegram Bot!**\n\n"
            "This is a simple test bot to verify Telegram integration.\n\n"
            "Use `/help` to see available commands."
        )

    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command"""
        help_text = (
            "📋 **Basic Bot Commands:**\n\n"
            "• `/start` - Show welcome message\n"
            "• `/help` - Show this help\n"
            "• `/test` - Send test message\n"
            "• `/ping` - Test connectivity\n\n"
            "🔧 **Status:** Bot is working correctly!"
        )
        await update.message.reply_text(help_text)

    async def test_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /test command"""
        import datetime
        test_msg = (
            f"🧪 **Test Message**\n\n"
            f"⏰ Time: {datetime.datetime.now().strftime('%H:%M:%S')}\n"
            f"👤 User: {update.effective_user.first_name}\n"
            f"💬 Chat ID: {update.effective_chat.id}\n\n"
            f"✅ Bot is responding correctly!"
        )
        await update.message.reply_text(test_msg)

    async def ping_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /ping command"""
        await update.message.reply_text("🏓 **Pong!** Bot is alive and responding.")

    async def send_message(self, text: str):
        """Send message to configured chat"""
        try:
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=text,
                parse_mode='Markdown'
            )
        except Exception as e:
            logger.error(f"Failed to send message: {e}")

async def main():
    """Main function"""
    try:
        bot = BasicTelegramBot()
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Bot stopped by user")
    except Exception as e:
        logger.error(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 