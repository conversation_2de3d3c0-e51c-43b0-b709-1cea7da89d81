# 🔄 Hướng dẫn Setup Gist Sync

Hệ thống này cho phép tự động đồng bộ `bot.sh` với GitHub Gist khi push code và cho phép users cập nhật local bằng `./bot.sh upgrade`.

## 📋 Cách hoạt động

```mermaid
graph LR
    A[Push bot.sh] --> B[GitHub Workflow]
    B --> C[Update Gist]
    C --> D[Gist Updated]
    E[User runs upgrade] --> F[Download from Gist]
    F --> G[Update local bot.sh]
```

## 🔑 Bước 1: Tạo GitHub Personal Access Token

1. **Truy cập GitHub Settings**
   - Đ<PERSON> đến: https://github.com/settings/tokens
   - Click "Generate new token" > "Generate new token (classic)"

2. **C<PERSON>u hình Token**
   - **Note**: `AutoTrader Gist Sync`
   - **Expiration**: 90 days (hoặc No expiration)
   - **Scopes**: ✅ Chọn `gist` (Write access to gists)

3. **Copy Token**
   - Click "Generate token"
   - **⚠️ Quan trọng**: Copy token ngay (sẽ không thể xem lại)

## 🔐 Bước 2: Thêm Token vào Repository Secrets

1. **Truy cập Repository Settings**
   - Đi đến repository: `https://github.com/hoangtrung99/autotrader`
   - Click tab "Settings"

2. **Add Secret**
   - Sidebar: "Secrets and variables" > "Actions"
   - Click "New repository secret"
   - **Name**: `GIST_TOKEN`
   - **Value**: Paste token từ bước 1
   - Click "Add secret"

## 🚀 Bước 3: Test Workflow

### Automatic Trigger (Push)
```bash
# Sửa bot.sh
nano bot.sh

# Commit và push
git add bot.sh
git commit -m "Update bot.sh"
git push origin main
```

### Manual Trigger
1. Đi đến tab "Actions" trong repository
2. Click workflow "Sync bot.sh to GitHub Gist"
3. Click "Run workflow"

## 📊 Bước 4: Verify Sync

### Check Workflow Status
- Tab "Actions" sẽ hiển thị workflow status
- Green checkmark = thành công
- Red X = có lỗi (check logs)

### Check Gist
- Truy cập: https://gist.github.com/hoangtrung99/73593690940ff91015063f2b6f9366a3
- File `autotrader.sh` sẽ có nội dung mới

### Test Upgrade Command
```bash
# Test upgrade functionality
./bot.sh upgrade
```

## 🛠️ Troubleshooting

### ❌ GIST_TOKEN không tìm thấy
```
❌ GIST_TOKEN secret not found!
```
**Giải pháp**: Check lại Bước 2, đảm bảo secret name là `GIST_TOKEN`

### ❌ Token không có quyền
```
❌ Failed to update gist!
Response: {"message":"Not Found"}
```
**Giải pháp**: 
- Token cần scope `gist`
- Tạo lại token với đúng permissions

### ❌ Gist không accessible
```
❌ Cannot access gist URL
```
**Giải pháp**:
- Đảm bảo gist là public
- Check internet connection
- Verify gist ID trong `bot.sh`

### ⚠️ File sizes differ
```
⚠️ File sizes differ - sync may need time to propagate
```
**Giải pháp**: GitHub cần thời gian để sync, thử lại sau 1-2 phút

## 🔧 Advanced Configuration

### Tùy chỉnh Gist URL
```bash
export BOT_GIST_URL='https://gist.githubusercontent.com/username/custom-gist-id/raw/custom-filename.sh'
./bot.sh upgrade
```

### Workflow Customization
Chỉnh sửa `.github/workflows/sync-gist.yml`:
- Thêm branches khác: `branches: [main, master, develop]`
- Thêm paths khác: `paths: ['bot.sh', 'scripts/*.sh']`
- Tùy chỉnh gist filename trong workflow

## 📋 Current Configuration

- **Repository**: `hoangtrung99/autotrader`
- **Gist ID**: `73593690940ff91015063f2b6f9366a3`
- **Gist File**: `autotrader.sh`
- **Raw URL**: https://gist.githubusercontent.com/hoangtrung99/73593690940ff91015063f2b6f9366a3/raw/autotrader.sh
- **Trigger**: Push to `main` branch với changes trong `bot.sh`

## 🎯 Usage Examples

### For Developers
```bash
# Update bot.sh
nano bot.sh

# Push to sync with gist
git add bot.sh
git commit -m "Add new feature"
git push

# Workflow sẽ tự động sync với gist
```

### For Users
```bash
# Download và cập nhật bot.sh mới nhất
./bot.sh upgrade

# Hoặc với traderbot command
traderbot upgrade
```

### Check Status
```bash
# Xem thông tin
./bot.sh info

# Test upgrade (dry run)
./bot.sh upgrade
```

## 📈 Benefits

- ✅ **Tự động sync**: Không cần manual update gist
- ✅ **Version control**: Track changes qua GitHub workflow
- ✅ **Easy distribution**: Users chỉ cần `./bot.sh upgrade`
- ✅ **Backup**: Workflow tự tạo backup trước khi update
- ✅ **Validation**: Kiểm tra file integrity trước install
- ✅ **Cross-platform**: Hoạt động trên Linux, macOS, Windows

## 🔒 Security Notes

- Token chỉ có quyền `gist`, không access repo
- Gist public nên không chứa sensitive data
- Local backup tự động trước mỗi upgrade
- Validation content trước khi install 