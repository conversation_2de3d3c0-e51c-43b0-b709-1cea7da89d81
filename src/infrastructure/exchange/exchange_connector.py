"""Exchange connector for cryptocurrency exchanges"""
import asyncio
import ccxt.async_support as ccxt
from typing import Dict, Optional, List, Any, Tuple, Union
import logging
from datetime import datetime, timedelta
import pandas as pd
import warnings
import json
import re

from src.core.models import Candle, Order, OrderType, OrderSide, OrderStatus


class ExchangeConnector:
    """Professional exchange connector with error handling and retry logic"""
    
    def __init__(self, config, credentials: Dict[str, str]):
        self.config = config
        self.credentials = credentials
        self.exchange: Optional[Union[ccxt.binance, ccxt.bybit]] = None
        self.logger = logging.getLogger('ExchangeConnector')
        
        # Rate limiting
        self.last_request_time = {}
        self.rate_limit_delay = 0.1  # 100ms between requests
        
        # Connection state
        self.is_connected = False
        self.retry_count = 0
        self.max_retries = 3
        
        # Track if close was called
        self._closed = False
    
    async def __aenter__(self):
        """Async context manager entry"""
        connected = await self.connect()
        if not connected:
            raise ConnectionError("Failed to connect to exchange")
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self.close()
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        if hasattr(self, 'exchange') and self.exchange and not self._closed:
            import asyncio
            try:
                # Try to close if we're in an async context
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self._force_close())
                else:
                    loop.run_until_complete(self._force_close())
            except Exception:
                # Fallback - just mark as closed since async close failed
                self._closed = True
    
    async def _force_close(self):
        """Force close exchange connection"""
        try:
            if self.exchange and not self._closed:
                await self.exchange.close()
                self._closed = True
        except Exception:
            pass
        
    async def connect(self) -> bool:
        """Connect to exchange"""
        try:
            exchange_id = self.config.exchange.lower()
            
            if exchange_id == 'binance':
                self.exchange = ccxt.binance({
                    'apiKey': self.credentials['api_key'],
                    'secret': self.credentials['api_secret'],
                    'sandbox': self.config.use_test_mode,
                    'enableRateLimit': True,
                    'rateLimit': 1200,  # 1200ms between requests
                
                })
            elif exchange_id == 'bybit':
                self.exchange = ccxt.bybit({
                    'apiKey': self.credentials['api_key'],
                    'secret': self.credentials['api_secret'],
                    'sandbox': self.config.use_test_mode,
                    'enableRateLimit': True,
                    'rateLimit': 100,  # 100ms between requests for Bybit
                    'options': {
                        'defaultType': 'linear',  # Use linear perpetual futures by default
                        'recvWindow': 5000,
                    }
                })
            else:
                raise ValueError(f"Unsupported exchange: {exchange_id}")
            
            # Test connection
            await self.exchange.load_markets()
            balance = await self.exchange.fetch_balance()
            
            self.is_connected = True
            self.logger.info(f"Connected to {exchange_id} exchange")
            
            # Get USDT balance for display - safe balance access
            usdt_balance = 0.0
            try:
                # Convert to Any to avoid TypedDict restrictions
                balance_dict = dict(balance) if isinstance(balance, dict) else {}
                
                # Try different balance structures
                if 'USDT' in balance_dict and isinstance(balance_dict['USDT'], dict):
                    free_amount = balance_dict['USDT'].get('free', 0)
                    usdt_balance = float(free_amount) if free_amount is not None else 0.0
                elif 'free' in balance_dict and isinstance(balance_dict['free'], dict):
                    free_balance = balance_dict['free']
                    if 'USDT' in free_balance:
                        free_amount = free_balance['USDT']
                        usdt_balance = float(free_amount) if free_amount is not None else 0.0
            except (KeyError, TypeError, ValueError) as e:
                self.logger.warning(f"Could not parse USDT balance: {e}")
                usdt_balance = 0.0
            
            self.logger.info(f"Available balance: {usdt_balance} USDT")
            
            return True
            
        except Exception as e:
            self.is_connected = False
            
            # Parse and enhance error message
            error_message = self._parse_exchange_error(str(e))
            self.logger.error(f"Exchange connection failed: {error_message}")
            
            # Raise specific exception with parsed error message
            raise ConnectionError(f"Failed to connect to {exchange_id}: {error_message}")
    
    def _parse_exchange_error(self, error_str: str) -> str:
        """Parse exchange API error and return user-friendly message"""
        try:
            # Try to extract JSON error from common patterns
            json_pattern = r'\{[^{}]*"retCode"[^{}]*\}'
            json_match = re.search(json_pattern, error_str)
            
            if json_match:
                try:
                    error_json = json.loads(json_match.group())
                    ret_code = error_json.get('retCode')
                    ret_msg = error_json.get('retMsg', '')
                    
                    # Map common error codes to English messages
                    error_code_map = {
                        10003: "Invalid API Key. Please check your API Key.",
                        10004: "Invalid API signature. Please check your API Secret.",
                        10005: "Insufficient API permissions. Please enable trading permissions for your API Key.",
                        33004: "API Key has expired. Please create a new API Key.",
                        10001: "Missing required parameters in request.",
                        10002: "Invalid request format.",
                        10006: "API Key is locked or restricted.",
                    }
                    
                    if ret_code in error_code_map:
                        return error_code_map[ret_code]
                    elif ret_msg:
                        return f"API Error ({ret_code}): {ret_msg}"
                    else:
                        return f"Unknown API error (code: {ret_code})"
                        
                except (json.JSONDecodeError, KeyError):
                    pass
            
            # Check for common network/connection errors
            error_str_lower = error_str.lower()
            
            if 'timeout' in error_str_lower:
                return "Connection timeout. Please check your internet connection and try again."
            elif 'network' in error_str_lower or 'connection' in error_str_lower:
                return "Network connection error. Please check your internet connection."
            elif 'api key' in error_str_lower and 'invalid' in error_str_lower:
                return "Invalid API Key. Please check your API Key."
            elif 'signature' in error_str_lower or 'secret' in error_str_lower:
                return "Invalid API Secret. Please check your API Secret."
            elif 'permission' in error_str_lower or 'forbidden' in error_str_lower:
                return "Access forbidden. Please check your API Key permissions."
            elif 'rate limit' in error_str_lower:
                return "Rate limit exceeded. Please try again in a few minutes."
            
            # Return original error if can't parse
            return error_str
            
        except Exception:
            # If all parsing fails, return original error
            return error_str
    
    async def close(self) -> None:
        """Close exchange connection"""
        if self.exchange and not self._closed:
            try:
                # Suppress warnings when closing exchange
                with warnings.catch_warnings():
                    warnings.simplefilter("ignore")
                    await self.exchange.close()
                self._closed = True
                self.is_connected = False
                self.logger.info("Exchange connection closed")
            except Exception as e:
                # Log at debug level only
                self.logger.debug(f"Exchange close completed with warnings (safe to ignore): {e}")
                self._closed = True
                self.is_connected = False
    
    async def _rate_limit(self, endpoint: str) -> None:
        """Apply rate limiting"""
        now = datetime.now()
        if endpoint in self.last_request_time:
            elapsed = (now - self.last_request_time[endpoint]).total_seconds()
            if elapsed < self.rate_limit_delay:
                await asyncio.sleep(self.rate_limit_delay - elapsed)
        
        self.last_request_time[endpoint] = now
    
    async def _retry_request(self, func, *args, **kwargs) -> Any:
        """Retry failed requests with exponential backoff"""
        for attempt in range(self.max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                if attempt == self.max_retries - 1:
                    raise e
                
                wait_time = 2 ** attempt
                self.logger.warning(f"Request failed (attempt {attempt + 1}), retrying in {wait_time}s: {e}")
                await asyncio.sleep(wait_time)
    
    def _ensure_connected(self) -> None:
        """Ensure exchange is connected"""
        if not self.exchange or not self.is_connected:
            raise ConnectionError("Exchange not connected. Call connect() first.")
    
    async def fetch_ticker(self, symbol: str) -> Dict[str, Any]:
        """Fetch current ticker data"""
        self._ensure_connected()
        await self._rate_limit('ticker')
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        return await self._retry_request(self.exchange.fetch_ticker, symbol)
    
    async def fetch_ohlcv(self, symbol: str, timeframe: str = '1m', 
                         limit: int = 100, since: Optional[int] = None) -> List[Candle]:
        """Fetch OHLCV data and convert to Candle objects"""
        self._ensure_connected()
        await self._rate_limit('ohlcv')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        ohlcv_data = await self._retry_request(
            self.exchange.fetch_ohlcv, symbol, timeframe, since, limit
        )
        
        candles = []
        for data in ohlcv_data:
            candle = Candle(
                timestamp=datetime.fromtimestamp(data[0] / 1000),
                open=float(data[1]),
                high=float(data[2]),
                low=float(data[3]),
                close=float(data[4]),
                volume=float(data[5])
            )
            candles.append(candle)
        
        return candles
    
    async def fetch_balance(self) -> Dict[str, Any]:
        """Fetch account balance"""
        self._ensure_connected()
        await self._rate_limit('balance')
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        return await self._retry_request(self.exchange.fetch_balance)
    
    async def fetch_positions(self) -> List[Dict[str, Any]]:
        """Fetch open positions (for futures)"""
        self._ensure_connected()
        await self._rate_limit('positions')
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        return await self._retry_request(self.exchange.fetch_positions)
    
    async def create_market_order(self, symbol: str, side: OrderSide, 
                                amount: float, params: Optional[Dict] = None) -> Order:
        """Create market order (supports both spot and perpetual futures)"""
        self._ensure_connected()
        await self._rate_limit('order')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        # Validate side parameter
        if side is None:
            raise ValueError("OrderSide parameter cannot be None")
        
        if not isinstance(side, OrderSide):
            raise ValueError(f"Expected OrderSide enum, got {type(side)}")
        
        if not hasattr(side, 'value') or side.value is None:
            raise ValueError(f"OrderSide.value is None for side: {side}")
        
        try:
            # Merge default params with provided params
            final_params = self._prepare_order_params(params)
            
            # Ensure side.value is valid string
            side_str = side.value.lower() if side.value is not None else 'buy'
            
            self.logger.debug(f"Creating market order: {symbol} {side_str} {amount} with params: {final_params}")
            
            order_data = await self._retry_request(
                self.exchange.create_market_order,
                symbol, side_str, amount, final_params
            )
            
            return self._convert_to_order(order_data)
            
        except Exception as e:
            self.logger.error(f"Market order failed: {e}")
            self.logger.error(f"Debug info - side: {side}, side.value: {getattr(side, 'value', 'NO_VALUE')}")
            raise
    
    async def create_limit_order(self, symbol: str, side: OrderSide, 
                               amount: float, price: float, 
                               params: Optional[Dict] = None) -> Order:
        """Create limit order (supports both spot and perpetual futures)"""
        self._ensure_connected()
        await self._rate_limit('order')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        # Validate side parameter
        if side is None:
            raise ValueError("OrderSide parameter cannot be None")
        
        if not isinstance(side, OrderSide):
            raise ValueError(f"Expected OrderSide enum, got {type(side)}")
        
        if not hasattr(side, 'value') or side.value is None:
            raise ValueError(f"OrderSide.value is None for side: {side}")
        
        try:
            # Merge default params with provided params
            final_params = self._prepare_order_params(params)
            
            # Ensure side.value is valid string
            side_str = side.value.lower() if side.value is not None else 'buy'
            
            self.logger.debug(f"Creating limit order: {symbol} {side_str} {amount} @ {price} with params: {final_params}")
            
            order_data = await self._retry_request(
                self.exchange.create_limit_order,
                symbol, side_str, amount, price, final_params
            )
            
            return self._convert_to_order(order_data)
            
        except Exception as e:
            self.logger.error(f"Limit order failed: {e}")
            self.logger.error(f"Debug info - side: {side}, side.value: {getattr(side, 'value', 'NO_VALUE')}")
            raise
    
    async def create_stop_loss_order(self, symbol: str, side: OrderSide,
                                   amount: float, stop_price: float,
                                   params: Optional[Dict] = None) -> Order:
        """Create stop loss order (supports both spot and perpetual futures)"""
        self._ensure_connected()
        await self._rate_limit('order')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        # Prepare stop loss params based on exchange
        order_params = self._prepare_order_params(params)
        
        # Add stop loss specific parameters
        if self.config.exchange.lower() == 'bybit':
            side_upper = side.value.upper() if side.value else 'BUY'
            order_params.update({
                'stopPrice': stop_price,
                'triggerDirection': 1 if side_upper == 'SELL' else 2,  # Bybit specific
                'orderType': 'Market',  # Market order when triggered
            })
        else:
            # Binance style
            order_params.update({
                'stopPrice': stop_price,
                'type': 'STOP_MARKET',
                'timeInForce': 'GTC'
            })
        
        # Validate side parameter
        if side is None:
            raise ValueError("OrderSide parameter cannot be None")
        
        if not isinstance(side, OrderSide):
            raise ValueError(f"Expected OrderSide enum, got {type(side)}")
        
        if not hasattr(side, 'value') or side.value is None:
            raise ValueError(f"OrderSide.value is None for side: {side}")
        
        try:
            # Ensure side.value is valid string
            side_str = side.value.lower() if side.value is not None else 'buy'
            
            self.logger.debug(f"Creating stop loss order: {symbol} {side_str} {amount} @ {stop_price} with params: {order_params}")
            
            order_data = await self._retry_request(
                self.exchange.create_order,
                symbol, 'stop', side_str, 
                amount, None, stop_price, order_params
            )
            
            return self._convert_to_order(order_data)
            
        except Exception as e:
            self.logger.error(f"Stop loss order failed: {e}")
            self.logger.error(f"Debug info - side: {side}, side.value: {getattr(side, 'value', 'NO_VALUE')}")
            raise
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        self._ensure_connected()
        await self._rate_limit('cancel_order')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        try:
            result = await self._retry_request(
                self.exchange.cancel_order, order_id, symbol
            )
            self.logger.info(f"Order {order_id} cancelled successfully")
            return result is not None
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def fetch_order_status(self, order_id: str, symbol: str) -> Order:
        """Fetch order status with Bybit optimization"""
        self._ensure_connected()
        await self._rate_limit('order_status')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        # For Bybit, try optimized methods first to avoid 500 orders limitation
        if self.config.exchange and self.config.exchange.lower() == 'bybit':
            try:
                # Try to fetch from open orders first (Bybit recommended approach)
                fetch_open_order = getattr(self.exchange, 'fetch_open_order', None)
                if fetch_open_order:
                    order_data = await self._retry_request(
                        fetch_open_order, order_id, symbol
                    )
                    return self._convert_to_order(order_data)
            except Exception:
                # If not in open orders, try closed orders
                try:
                    fetch_closed_order = getattr(self.exchange, 'fetch_closed_order', None)
                    if fetch_closed_order:
                        order_data = await self._retry_request(
                            fetch_closed_order, order_id, symbol
                        )
                        return self._convert_to_order(order_data)
                except Exception:
                    # Fallback to regular fetchOrder with acknowledged param
                    pass
        
        # Default behavior with acknowledged param for Bybit
        params = {}
        if self.config.exchange and self.config.exchange.lower() == 'bybit':
            params['acknowledged'] = True
            
        order_data = await self._retry_request(
            self.exchange.fetch_order, order_id, symbol, params
        )
        
        return self._convert_to_order(order_data)
    
    async def fetch_my_trades(self, symbol: str, since: Optional[int] = None,
                            limit: int = 100) -> List[Dict[str, Any]]:
        """Fetch trade history"""
        self._ensure_connected()
        await self._rate_limit('trades')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        return await self._retry_request(
            self.exchange.fetch_my_trades, symbol, since, limit
        )
    
    async def get_open_orders(self, symbol: str) -> List[Dict[str, Any]]:
        """Get all open orders for a symbol"""
        self._ensure_connected()
        await self._rate_limit('open_orders')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        try:
            orders = await self._retry_request(
                self.exchange.fetch_open_orders, symbol
            )
            self.logger.debug(f"Fetched {len(orders)} open orders for {symbol}")
            return orders
            
        except Exception as e:
            self.logger.error(f"Failed to fetch open orders for {symbol}: {e}")
            return []
    
    def _prepare_order_params(self, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Prepare order parameters based on exchange and trading type"""
        final_params = params.copy() if params else {}
        
        # Add exchange-specific parameters for perpetual futures
        if self.config.exchange and self.config.exchange.lower() == 'bybit':
            # Bybit linear perpetual futures parameters
            final_params.setdefault('category', 'linear')
            final_params.setdefault('timeInForce', 'GTC')
            
        elif self.config.exchange and self.config.exchange.lower() == 'binance':
            # Binance futures parameters
            final_params.setdefault('timeInForce', 'GTC')
        
        # Ensure all params are properly formatted
        cleaned_params = {}
        for key, value in final_params.items():
            if value is not None:
                cleaned_params[key] = value
            
        return cleaned_params
    
    def _convert_to_order(self, order_data: Dict[str, Any]) -> Order:
        """Convert exchange order data to Order model"""
        # Map exchange order status to our OrderStatus enum
        status_mapping = {
            'open': OrderStatus.OPEN,
            'closed': OrderStatus.FILLED,
            'filled': OrderStatus.FILLED,
            'canceled': OrderStatus.CANCELLED,
            'cancelled': OrderStatus.CANCELLED,
            'rejected': OrderStatus.REJECTED,
            'expired': OrderStatus.EXPIRED,
            'pending': OrderStatus.PENDING,
            'partial': OrderStatus.PARTIALLY_FILLED,
            'partially_filled': OrderStatus.PARTIALLY_FILLED
        }
        
        # Parse order status safely
        raw_status = order_data.get('status') or 'pending'
        if raw_status is None:
            raw_status = 'pending'
        status = status_mapping.get(raw_status.lower(), OrderStatus.PENDING)
        
        # Parse order type safely
        order_type_str = order_data.get('type') or ''
        order_type = OrderType.MARKET if order_type_str.upper() == 'MARKET' else OrderType.LIMIT
        
        # Parse order side safely
        side_str = order_data.get('side') or ''
        side = OrderSide.BUY if side_str.upper() == 'BUY' else OrderSide.SELL
        
        # Parse timestamps
        created_at = datetime.now()
        if 'timestamp' in order_data and order_data['timestamp']:
            try:
                created_at = datetime.fromtimestamp(order_data['timestamp'] / 1000)
            except (ValueError, TypeError):
                pass
        
        filled_at = None
        if status == OrderStatus.FILLED and 'datetime' in order_data and order_data['datetime']:
            try:
                datetime_str = order_data['datetime']
                if datetime_str and isinstance(datetime_str, str):
                    filled_at = datetime.fromisoformat(datetime_str.replace('Z', '+00:00'))
            except (ValueError, TypeError):
                pass
        
        # Calculate filled amount and average price safely
        filled_amount = float(order_data.get('filled') or 0)
        
        # Parse average price safely
        avg_price_raw = order_data.get('average')
        if avg_price_raw:
            average_price = float(avg_price_raw)
        else:
            price_raw = order_data.get('price')
            average_price = float(price_raw) if price_raw else 0.0
        
        # Parse other values safely
        price_raw = order_data.get('price')
        order_price = float(price_raw) if price_raw else 0.0
        
        amount_raw = order_data.get('amount')
        order_amount = float(amount_raw) if amount_raw else 0.0
        
        # Parse fee safely
        fee_amount = 0.0
        fee_data = order_data.get('fee')
        if fee_data and isinstance(fee_data, dict):
            fee_cost = fee_data.get('cost')
            if fee_cost:
                fee_amount = float(fee_cost)
        
        return Order(
            symbol=order_data.get('symbol', ''),
            side=side,
            type=order_type,
            price=order_price,
            amount=order_amount,
            status=status,
            order_id=order_data.get('id'),
            filled_amount=filled_amount,
            average_price=average_price,
            fee=fee_amount,
            created_at=created_at,
            filled_at=filled_at
        )
    
    async def get_trading_fees(self, symbol: str) -> Dict[str, float]:
        """Get trading fees for symbol"""
        self._ensure_connected()
        await self._rate_limit('fees')
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        try:
            fees = await self._retry_request(self.exchange.fetch_trading_fees)
            return fees.get(symbol, {'maker': 0.001, 'taker': 0.001})
            
        except Exception as e:
            self.logger.warning(f"Could not fetch fees: {e}")
            # Default fees for different exchanges
            if self.config.exchange.lower() == 'bybit':
                return {'maker': 0.001, 'taker': 0.001}  # Bybit spot fees
            else:
                return {'maker': 0.001, 'taker': 0.001}  # Default fees
    
    async def get_market_info(self, symbol: str) -> Dict[str, Any]:
        """Get market information"""
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
            
        if not self.exchange.markets:
            await self.exchange.load_markets()
        
        markets = getattr(self.exchange, 'markets', None)
        return markets.get(symbol, {}) if markets else {}
    
    def validate_order_params(self, symbol: str, amount: float, 
                            price: Optional[float] = None) -> Tuple[bool, str]:
        """Validate order parameters"""
        if self.exchange is None:
            return False, "Exchange not initialized"
            
        try:
            markets = getattr(self.exchange, 'markets', None)
            market = markets.get(symbol) if markets else None
            if not market:
                return False, f"Market {symbol} not found"
            
            # Check minimum amount
            min_amount = market.get('limits', {}).get('amount', {}).get('min', 0)
            if amount < min_amount:
                return False, f"Amount {amount} below minimum {min_amount}"
            
            # Check price precision for limit orders
            if price:
                price_precision = market.get('precision', {}).get('price', 8)
                if len(str(price).split('.')[-1]) > price_precision:
                    return False, f"Price precision exceeds {price_precision} decimals"
            
            return True, "Valid"
            
        except Exception as e:
            return False, f"Validation error: {e}"
    
    async def edit_order(self, order_id: str, symbol: str, type: str, side: OrderSide,
                        amount: float, price: float, params: Optional[Dict] = None) -> Optional[Order]:
        """Edit/modify an existing order by canceling and recreating"""
        self._ensure_connected()
        
        if self.exchange is None:
            raise ConnectionError("Exchange not initialized")
        
        try:
            # Cancel the existing order first
            cancel_success = await self.cancel_order(order_id, symbol)
            if not cancel_success:
                self.logger.warning(f"Failed to cancel order {order_id}, proceeding with new order anyway")
            
            # Wait a bit for cancel to process
            await asyncio.sleep(0.1)
            
            # Create new order with updated parameters
            if type == 'limit':
                new_order = await self.create_limit_order(symbol, side, amount, price, params)
            elif type == 'market':
                new_order = await self.create_market_order(symbol, side, amount, params)
            else:
                raise ValueError(f"Unsupported order type for editing: {type}")
            
            if new_order:
                self.logger.info(f"Order {order_id} replaced with new order {new_order.order_id}: {side.value} {amount:.6f} @ ${price:.6f}")
                return new_order
            else:
                self.logger.error(f"Failed to create replacement order for {order_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"Failed to edit order {order_id}: {e}")
            return None 