"""Telegram Event Integration - Connect trading events with notifications"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime

from .events.trading_event_listener import TradingEventListener, EventType
from .events.exchange_event_handler import ExchangeEventHandler
from .telegram_notification_manager import TelegramNotificationManager

try:
    from telegram import InlineKeyboardButton, InlineKeyboardMarkup
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TelegramEventIntegration:
    """Integrates trading events with Telegram notifications"""
    
    def __init__(self, notification_manager: TelegramNotificationManager, bot):
        self.logger = logging.getLogger('TelegramEventIntegration')
        self.notification_manager = notification_manager
        self.bot = bot
        
        # Create event handler with bot reference
        self.event_handler = ExchangeEventHandler(notification_manager, bot)
        
        # Integration settings
        self.auto_start = True
        self.monitoring_active = False
        
        # Commands for managing event monitoring
        self.commands = {
            '/eventstart': self.handle_event_start_command,
            '/eventstop': self.handle_event_stop_command,
            '/eventstatus': self.handle_event_status_command,
            '/eventconfig': self.handle_event_config_command,
            '/eventfilters': self.handle_event_filters_command
        }
    
    # ===============================
    # Event Monitoring Commands
    # ===============================
    
    async def handle_event_start_command(self, update, context):
        """Handle /eventstart command - Start monitoring trading events"""
        try:
            chat_id = update.effective_chat.id
            
            if self.monitoring_active:
                await self._send_message(
                    chat_id,
                    "ℹ️ **Event monitoring is already active**\n\n"
                    "Use `/eventstatus` to check current status."
                )
                return
            
            # Default exchange configuration
            exchange_configs = {
                'bybit': ['HYPER/USDT:USDT', 'BTC/USDT:USDT', 'ETH/USDT:USDT'],
                'binance': ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
            }
            
            # Start monitoring
            await self.event_handler.start_monitoring(exchange_configs)
            self.monitoring_active = True
            
            keyboard = self._create_keyboard([
                [("📊 Event Status", "event_status"), ("⚙️ Configure", "event_config")],
                [("🔇 Stop Events", "event_stop"), ("❌ Close", "close")]
            ])
            
            await self._send_message(
                chat_id,
                "✅ **Event Monitoring Started**\n\n"
                "🔊 Now monitoring trading events from:\n"
                "• Bybit: HYPER, BTC, ETH\n"
                "• Binance: BTC, ETH, BNB\n\n"
                "📡 **Auto-notifications enabled for:**\n"
                "• Order fills\n"
                "• Take profit triggers\n" 
                "• Stop loss triggers\n"
                "• Position liquidations\n\n"
                "💡 Use `/subscribe` to enable notifications.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self._send_message(
                update.effective_chat.id,
                f"❌ **Error starting event monitoring:**\n\n`{str(e)}`"
            )
    
    async def handle_event_stop_command(self, update, context):
        """Handle /eventstop command - Stop monitoring trading events"""
        try:
            chat_id = update.effective_chat.id
            
            if not self.monitoring_active:
                await self._send_message(
                    chat_id,
                    "ℹ️ **Event monitoring is not active**\n\n"
                    "Use `/eventstart` to start monitoring."
                )
                return
            
            # Confirmation keyboard
            keyboard = self._create_keyboard([
                [("✅ Confirm Stop", "event_stop_confirm"), ("❌ Cancel", "close")]
            ])
            
            await self._send_message(
                chat_id,
                "⚠️ **Confirm Stop Event Monitoring**\n\n"
                "This will stop all trading event monitoring.\n"
                "You will no longer receive real-time alerts.\n\n"
                "Are you sure?",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self._send_message(
                update.effective_chat.id,
                f"❌ **Error:**\n\n`{str(e)}`"
            )
    
    async def handle_event_status_command(self, update, context):
        """Handle /eventstatus command - Show event monitoring status"""
        try:
            chat_id = update.effective_chat.id
            
            if not self.monitoring_active:
                await self._send_message(
                    chat_id,
                    "📊 **Event Monitoring Status**\n\n"
                    "❌ **Status:** Inactive\n\n"
                    "Use `/eventstart` to start monitoring trading events."
                )
                return
            
            # Get statistics
            stats = self.event_handler.get_event_statistics()
            
            message = "📊 **Event Monitoring Status**\n\n"
            message += "✅ **Status:** Active\n\n"
            
            # Listener statistics
            listener_stats = stats.get('event_listener', {})
            message += "**📡 Event Listener:**\n"
            message += f"• Events received: `{listener_stats.get('events_received', 0)}`\n"
            message += f"• Events processed: `{listener_stats.get('events_processed', 0)}`\n"
            message += f"• Events filtered: `{listener_stats.get('events_filtered', 0)}`\n"
            message += f"• Active exchanges: `{listener_stats.get('active_exchanges', 0)}`\n\n"
            
            # Connection status
            connections = stats.get('connections', {})
            message += "**🔗 Exchange Connections:**\n"
            for exchange, status in connections.items():
                status_emoji = "🟢" if status == 'connected' else "🔴"
                message += f"{status_emoji} {exchange.title()}: `{status}`\n"
            message += "\n"
            
            # Daily performance
            daily_perf = stats.get('daily_performance', {})
            if daily_perf.get('trades', 0) > 0:
                message += "**📈 Today's Performance:**\n"
                message += f"• Trades: `{daily_perf.get('trades', 0)}`\n"
                message += f"• Profit: `+${daily_perf.get('profit', 0):.2f}`\n"
                message += f"• Loss: `-${daily_perf.get('loss', 0):.2f}`\n"
                net_pnl = daily_perf.get('profit', 0) - daily_perf.get('loss', 0)
                message += f"• Net P&L: `${net_pnl:+.2f}`\n\n"
            
            # Subscribers
            subscriber_count = stats.get('notification_subscribers', 0)
            message += f"**👥 Notification Subscribers:** `{subscriber_count}`\n"
            message += f"**🏃‍♂️ Active Positions:** `{stats.get('active_positions', 0)}`"
            
            keyboard = self._create_keyboard([
                [("🔄 Refresh", "event_status"), ("⚙️ Configure", "event_config")],
                [("🔇 Stop Events", "event_stop"), ("📊 Filters", "event_filters")],
                [("❌ Close", "close")]
            ])
            
            await self._send_message(
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self._send_message(
                update.effective_chat.id,
                f"❌ **Error getting status:**\n\n`{str(e)}`"
            )
    
    async def handle_event_config_command(self, update, context):
        """Handle /eventconfig command - Configure event monitoring"""
        try:
            chat_id = update.effective_chat.id
            
            # Get current auto-notify events
            auto_events = self.event_handler.auto_notify_events
            
            message = "⚙️ **Event Configuration**\n\n"
            message += "**🔔 Auto-notification Events:**\n"
            
            all_events = [
                (EventType.ORDER_FILLED, "Order executions"),
                (EventType.TAKE_PROFIT_TRIGGERED, "Take profit triggers"),
                (EventType.STOP_LOSS_TRIGGERED, "Stop loss triggers"),
                (EventType.POSITION_LIQUIDATED, "Position liquidations"),
                (EventType.POSITION_OPENED, "Position opens"),
                (EventType.POSITION_CLOSED, "Position closes"),
                (EventType.BALANCE_UPDATE, "Balance changes"),
                (EventType.ORDER_CANCELLED, "Order cancellations")
            ]
            
            for event_type, description in all_events:
                enabled = event_type in auto_events
                emoji = "✅" if enabled else "❌"
                message += f"{emoji} {description}\n"
            
            message += "\n💡 Toggle notifications for specific event types:"
            
            keyboard_buttons = []
            for event_type, description in all_events[:4]:  # First 4 events
                enabled = event_type in auto_events
                action = "disable" if enabled else "enable"
                keyboard_buttons.append([(f"{'🔕' if enabled else '🔔'} {description[:15]}...", f"event_toggle_{event_type.value}_{action}")])
            
            keyboard_buttons.append([("🔄 Refresh", "event_config"), ("❌ Close", "close")])
            keyboard = self._create_keyboard(keyboard_buttons)
            
            await self._send_message(
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self._send_message(
                update.effective_chat.id,
                f"❌ **Error:**\n\n`{str(e)}`"
            )
    
    async def handle_event_filters_command(self, update, context):
        """Handle /eventfilters command - Manage event filters"""
        try:
            chat_id = update.effective_chat.id
            
            # Get current filters
            filters = self.event_handler.event_listener.event_filters
            
            message = "📊 **Event Filters**\n\n"
            message += "**🔍 Active Filters:**\n"
            
            if filters:
                for filter_name in filters.keys():
                    message += f"• `{filter_name}`\n"
                message += f"\n**Total:** {len(filters)} filters active"
            else:
                message += "❌ No filters are currently active.\n"
                message += "All events will be processed."
            
            message += "\n\n**📝 Available Filters:**\n"
            message += "• `small_orders` - Filter orders below $10\n"
            message += "• `test_symbols` - Filter test/demo symbols\n"
            message += "• `weekend_trades` - Filter weekend trading\n"
            message += "• `low_volume` - Filter low volume events"
            
            keyboard = self._create_keyboard([
                [("➕ Add Filter", "filter_add"), ("➖ Remove Filter", "filter_remove")],
                [("🔄 Refresh", "event_filters"), ("❌ Close", "close")]
            ])
            
            await self._send_message(
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self._send_message(
                update.effective_chat.id,
                f"❌ **Error:**\n\n`{str(e)}`"
            )
    
    # ===============================
    # Integration Lifecycle
    # ===============================
    
    async def initialize(self):
        """Initialize the event integration system"""
        try:
            self.logger.info("Initializing Telegram Event Integration...")
            
            if self.auto_start:
                # Auto-start with default configuration
                exchange_configs = {
                    'bybit': ['HYPER/USDT:USDT', 'BTC/USDT:USDT'],
                    'binance': ['BTCUSDT', 'ETHUSDT']
                }
                
                await self.event_handler.start_monitoring(exchange_configs)
                self.monitoring_active = True
                
                self.logger.info("✅ Event monitoring auto-started")
            
        except Exception as e:
            self.logger.error(f"Error initializing event integration: {e}")
    
    async def shutdown(self):
        """Shutdown the event integration system"""
        try:
            if self.monitoring_active:
                await self.event_handler.stop_monitoring()
                self.monitoring_active = False
                
            self.logger.info("✅ Event integration shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")
    
    # ===============================
    # Command Registration
    # ===============================
    
    def get_command_handlers(self) -> Dict[str, Callable]:
        """Get all command handlers for registration"""
        return self.commands
    
    def get_help_text(self) -> str:
        """Get help text for event commands"""
        return (
            "**🔊 Event Monitoring Commands:**\n"
            "• `/eventstart` - Start monitoring trading events\n"
            "• `/eventstop` - Stop event monitoring\n"
            "• `/eventstatus` - Show monitoring status\n"
            "• `/eventconfig` - Configure notifications\n"
            "• `/eventfilters` - Manage event filters\n"
        )
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for event integration"""
        try:
            if data == "event_status":
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_event_status_command(fake_update, fake_context)
                return True
            
            elif data == "event_config":
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_event_config_command(fake_update, fake_context)
                return True
            
            elif data == "event_stop":
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_event_stop_command(fake_update, fake_context)
                return True
            
            elif data == "event_stop_confirm":
                await self.event_handler.stop_monitoring()
                self.monitoring_active = False
                
                await query.edit_message_text(
                    "✅ **Event monitoring stopped**\n\n"
                    "Trading event monitoring has been disabled.\n\n"
                    "Use `/eventstart` to resume monitoring."
                )
                return True
            
            elif data.startswith("event_toggle_"):
                parts = data.split("_")
                event_name = parts[2]
                action = parts[3]
                
                # Find event type
                event_type = None
                for et in EventType:
                    if et.value == event_name:
                        event_type = et
                        break
                
                if event_type:
                    current_events = set(self.event_handler.auto_notify_events)
                    
                    if action == "enable":
                        current_events.add(event_type)
                        await query.answer("✅ Event notifications enabled")
                    else:
                        current_events.discard(event_type)
                        await query.answer("🔕 Event notifications disabled")
                    
                    self.event_handler.configure_auto_notifications(list(current_events))
                    
                    # Refresh the config view
                    from types import SimpleNamespace
                    fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                    fake_context = SimpleNamespace(bot=query.bot)
                    await self.handle_event_config_command(fake_update, fake_context)
                
                return True
            
            elif data == "event_filters":
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_event_filters_command(fake_update, fake_context)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in event callback handler: {e}")
            await query.edit_message_text(f"❌ Error: {str(e)}")
            return True
    
    # ===============================
    # Helper Methods
    # ===============================
    
    async def _send_message(self, chat_id: int, text: str, reply_markup=None):
        """Send message via bot"""
        try:
            await self.bot.send_message(
                chat_id=chat_id,
                text=text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
    
    def _create_keyboard(self, buttons):
        """Create inline keyboard"""
        try:
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup
            keyboard = []
            for row in buttons:
                keyboard_row = []
                for text, callback_data in row:
                    keyboard_row.append(InlineKeyboardButton(text, callback_data=callback_data))
                keyboard.append(keyboard_row)
            return InlineKeyboardMarkup(keyboard)
        except ImportError:
            self.logger.warning("Telegram library not available")
            return None
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            'active': self.monitoring_active,
            'auto_start': self.auto_start,
            'statistics': self.event_handler.get_event_statistics() if self.monitoring_active else None
        } 