"""Telegram Bot Manager - Handle trading bot containers via Telegram"""
from typing import Dict, List, Optional, Any
from .telegram_base import Telegram<PERSON><PERSON><PERSON>and<PERSON>, ValidationUtils, MessageFormatter

try:
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TelegramBotManager(TelegramBaseHandler):
    """Manages trading bot containers through Telegram interface"""
    
    def __init__(self, session_manager):
        super().__init__('TelegramBotManager')
        self.session_manager = session_manager
    
    # ===============================
    # Container Management Commands
    # ===============================
    
    async def handle_list_command(self, update, context):
        """Handle /list command - List all containers"""
        try:
            command = ["./bot.sh", "list"]
            response = await self.execute_botsh_with_response(command)
            
            # Create management keyboard
            keyboard = self.create_keyboard([
                [("🔄 Refresh", "bot_list"), ("▶️ Start All", "bot_start_all")],
                [("⏹️ Stop All", "bot_stop_all"), ("📊 Stats", "bot_stats")],
                [("➕ Create New", "bot_create"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"🤖 <b>Trading Bot Containers</b>\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "list")
            )
    
    async def handle_status_command(self, update, context):
        """Handle /status command - Show detailed status"""
        try:
            container_name = context.args[0] if context.args else None
            
            if container_name:
                command = ["./bot.sh", "status", container_name]
                response = await self.execute_botsh_with_response(command)
                
                # Create specific container management keyboard
                keyboard = self.create_keyboard([
                    [("▶️ Start", f"bot_start_{container_name}"), ("⏹️ Stop", f"bot_stop_{container_name}")],
                    [("🔄 Restart", f"bot_restart_{container_name}"), ("📋 Logs", f"bot_logs_{container_name}")],
                    [("🗑️ Remove", f"bot_remove_{container_name}"), ("❌ Close", "close")]
                ])
                
            else:
                command = ["./bot.sh", "status"]
                response = await self.execute_botsh_with_response(command)
                
                keyboard = self.create_keyboard([
                    [("📋 List All", "bot_list"), ("🔄 Refresh", "bot_status")],
                    [("❌ Close", "close")]
                ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"📊 <b>Container Status</b>\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "status")
            )
    
    async def handle_logs_command(self, update, context):
        """Handle /logs command - Show container logs"""
        try:
            if not context.args:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "📋 <b>View Container Logs</b>\n\n"
                    "Usage: `/logs <container_name> [lines]`\n\n"
                    "Examples:\n"
                    "• `/logs trader_bot` - Last 50 lines\n"
                    "• `/logs trader_bot 100` - Last 100 lines\n\n"
                    "Use `/list` to see available containers."
                )
                return
            
            container_name = context.args[0]
            lines = context.args[1] if len(context.args) > 1 else "50"
            
            command = ["./bot.sh", "logs", container_name, lines]
            response = await self.execute_botsh_with_response(command)
            
            # Create log management keyboard
            keyboard = self.create_keyboard([
                [("🔄 Refresh", f"bot_logs_{container_name}"), ("📊 Status", f"bot_status_{container_name}")],
                [("📋 List All", "bot_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"📋 <b>Logs: {container_name}</b>\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "logs")
            )
    
    async def handle_stop_command(self, update, context):
        """Handle /stop command - Stop specific container"""
        try:
            if not context.args:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "⏹️ <b>Stop Container</b>\n\n"
                    "Usage: `/stop <container_name>`\n\n"
                    "Example: `/stop trader_bot`\n\n"
                    "Use `/list` to see running containers."
                )
                return
            
            container_name = context.args[0]
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("✅ Yes, Stop", f"bot_confirm_stop_{container_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"⏹️ <b>Confirm Stop</b>\n\n"
                f"Are you sure you want to stop container: `{container_name}`?\n\n"
                f"⚠️ This will halt all trading activity.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "stop")
            )
    
    async def handle_restart_command(self, update, context):
        """Handle /restart command - Restart specific container"""
        try:
            if not context.args:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "🔄 <b>Restart Container</b>\n\n"
                    "Usage: `/restart <container_name>`\n\n"
                    "Example: `/restart trader_bot`\n\n"
                    "Use `/list` to see available containers."
                )
                return
            
            container_name = context.args[0]
            
            command = ["./bot.sh", "restart", container_name]
            response = await self.execute_botsh_with_response(command)
            
            # Create post-restart keyboard
            keyboard = self.create_keyboard([
                [("📊 Status", f"bot_status_{container_name}"), ("📋 Logs", f"bot_logs_{container_name}")],
                [("📋 List All", "bot_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"🔄 <b>Restart Complete</b>\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "restart")
            )
    
    async def handle_stopall_command(self, update, context):
        """Handle /stopall command - Stop all containers"""
        try:
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("✅ Yes, Stop All", "bot_confirm_stop_all"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "🛑 <b>Stop All Containers</b>\n\n"
                "Are you sure you want to stop ALL trading containers?\n\n"
                "⚠️ This will halt ALL trading activity!\n"
                "💡 Individual containers can be restarted later.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "stopall")
            )
    
    # ===============================
    # Advanced Bot Creation Wizard
    # ===============================
    
    async def handle_createbot_wizard(self, update, context):
        """Handle /createbot command - Start bot creation wizard"""
        try:
            user_id = update.effective_user.id
            
            # Clear any existing wizard
            self.session_manager.clear_session(user_id)
            
            # Check for credentials first
            returncode, stdout, stderr = await self.execute_botsh_command(["./bot.sh", "creds", "list"])
            
            if returncode != 0 or "No credentials found" in stdout:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "❌ <b>No Credentials Found</b>\n\n"
                    "You need to setup API credentials first.\n\n"
                    "Use `/addcreds` to add credentials, then try again."
                )
                return
            
            # Start wizard
            self.session_manager.start_wizard(user_id, 'create_bot')
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "🤖 <b>Create New Trading Bot Wizard</b>\n\n"
                "Step 1/7: What symbol do you want to trade?\n\n"
                "Examples:\n"
                "• `HYPER/USDT:USDT`\n"
                "• `BTC/USDT:USDT`\n"
                "• `ETH/USDT:USDT`\n\n"
                "📝 Enter trading symbol (or /cancel to abort):"
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "createbot")
            )
    
    async def handle_startbot_command(self, update, context):
        """Handle /startbot command - Quick start with parameters"""
        try:
            if len(context.args) < 2:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "▶️ <b>Quick Start Trading Bot</b>\n\n"
                    "Usage: `/startbot <symbol> <amount> [options]`\n\n"
                    "Examples:\n"
                    "• `/startbot HYPER/USDT:USDT 10`\n"
                    "• `/startbot BTC/USDT:USDT 50 --test-mode`\n"
                    "• `/startbot ETH/USDT:USDT 100 --stop-loss=5 --take-profit=10`\n\n"
                    "💡 For step-by-step setup use: `/createbot`"
                )
                return
            
            symbol = context.args[0]
            amount = context.args[1]
            options = context.args[2:] if len(context.args) > 2 else []
            
            # Validate inputs
            if not ValidationUtils.validate_symbol(symbol):
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "❌ Invalid symbol format. Use format like: HYPER/USDT:USDT"
                )
                return
            
            if not ValidationUtils.validate_amount(amount):
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "❌ Invalid amount. Must be a positive number."
                )
                return
            
            # Build command
            command = ["./bot.sh", "start", symbol, amount] + options
            response = await self.execute_botsh_with_response(command)
            
            # Create post-start keyboard
            keyboard = self.create_keyboard([
                [("📊 Status", "bot_status"), ("📋 Logs", "bot_logs_trader")],
                [("📋 List All", "bot_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"▶️ <b>Bot Started</b>\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "startbot")
            )
    
    # ===============================
    # Wizard Input Handler
    # ===============================
    
    async def handle_wizard_input(self, update, context) -> bool:
        """Handle text input for bot creation wizards. Returns True if handled."""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            return False
        
        session = self.session_manager.get_session(user_id)
        wizard_state = session.get('wizard_state')
        
        if wizard_state != 'create_bot':
            return False
        
        return await self._handle_create_bot_input(update, context)
    
    async def _handle_create_bot_input(self, update, context) -> bool:
        """Handle input for create bot wizard"""
        user_id = update.effective_user.id
        text = update.message.text.strip()
        wizard_data = self.session_manager.get_wizard_data(user_id)
        
        try:
            # Step 1: Trading symbol
            if 'symbol' not in wizard_data:
                if not ValidationUtils.validate_symbol(text):
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid symbol format. Use format like: HYPER/USDT:USDT\n\n"
                        "Please enter a valid trading symbol:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'symbol', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Symbol: `{text}`\n\n"
                    "Step 2/7: Enter trading amount (in USDT)\n\n"
                    "Examples: `10`, `50`, `100`\n\n"
                    "💰 Enter amount:"
                )
                return True
            
            # Step 2: Amount
            elif 'amount' not in wizard_data:
                if not ValidationUtils.validate_amount(text):
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid amount. Must be a positive number.\n\n"
                        "Please enter a valid amount:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'amount', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Amount: `{text} USDT`\n\n"
                    "Step 3/7: Trading direction\n\n"
                    "Available options:\n"
                    "• `long` - Only long positions\n"
                    "• `short` - Only short positions\n"
                    "• `both` - Both directions\n\n"
                    "📈 Enter direction:"
                )
                return True
            
            # Step 3: Direction
            elif 'direction' not in wizard_data:
                if text.lower() not in ['long', 'short', 'both']:
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid direction. Must be: long, short, or both\n\n"
                        "Please enter a valid direction:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'direction', text.lower())
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Direction: `{text.lower()}`\n\n"
                    "Step 4/7: Enable test mode?\n\n"
                    "• `yes` - Paper trading (recommended for testing)\n"
                    "• `no` - Live trading (real money)\n\n"
                    "🧪 Test mode:"
                )
                return True
            
            # Step 4: Test mode
            elif 'test_mode' not in wizard_data:
                test_mode = text.lower() in ['yes', 'y', 'true', '1']
                self.session_manager.update_wizard_data(user_id, 'test_mode', test_mode)
                
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Test mode: `{'Enabled' if test_mode else 'Disabled'}`\n\n"
                    "Step 5/7: Stop loss percentage (optional)\n\n"
                    "Examples: `5` (5%), `10` (10%)\n"
                    "Enter percentage or 'skip':\n\n"
                    "📉 Stop loss %:"
                )
                return True
            
            # Step 5: Stop loss
            elif 'stop_loss' not in wizard_data:
                if text.lower() == 'skip':
                    stop_loss = None
                else:
                    if not ValidationUtils.validate_percentage(text):
                        await self.send_message(
                            context.bot,
                            update.effective_chat.id,
                            "❌ Invalid percentage. Must be between 0-100.\n\n"
                            "Enter percentage or 'skip':"
                        )
                        return True
                    stop_loss = float(text)
                
                self.session_manager.update_wizard_data(user_id, 'stop_loss', stop_loss)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Stop loss: `{stop_loss}%` {'(disabled)' if stop_loss is None else ''}\n\n"
                    "Step 6/7: Take profit percentage (optional)\n\n"
                    "Examples: `10` (10%), `20` (20%)\n"
                    "Enter percentage or 'skip':\n\n"
                    "📈 Take profit %:"
                )
                return True
            
            # Step 6: Take profit
            elif 'take_profit' not in wizard_data:
                if text.lower() == 'skip':
                    take_profit = None
                else:
                    if not ValidationUtils.validate_percentage(text):
                        await self.send_message(
                            context.bot,
                            update.effective_chat.id,
                            "❌ Invalid percentage. Must be between 0-100.\n\n"
                            "Enter percentage or 'skip':"
                        )
                        return True
                    take_profit = float(text)
                
                self.session_manager.update_wizard_data(user_id, 'take_profit', take_profit)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Take profit: `{take_profit}%` {'(disabled)' if take_profit is None else ''}\n\n"
                    "Step 7/7: Container name (optional)\n\n"
                    "Examples: `my_trader`, `btc_bot`\n"
                    "Enter name or 'auto' for automatic:\n\n"
                    "🏷️ Container name:"
                )
                return True
            
            # Step 7: Container name & final confirmation
            elif 'container_name' not in wizard_data:
                if text.lower() == 'auto':
                    symbol = wizard_data['symbol'].replace('/', '_').replace(':', '_')
                    container_name = f"trader_{symbol.lower()}"
                else:
                    container_name = text.strip()
                
                self.session_manager.update_wizard_data(user_id, 'container_name', container_name)
                
                # Create summary
                summary = self._create_bot_summary(wizard_data, container_name)
                
                keyboard = self.create_keyboard([
                    [("🚀 Start Bot", "bot_create_confirm"), ("❌ Cancel", "bot_create_cancel")]
                ])
                
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"📋 <b>Bot Configuration Summary</b>\n\n{summary}\n\nReady to start?",
                    reply_markup=keyboard
                )
                return True
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "wizard input")
            )
            self.session_manager.finish_wizard(user_id)
        
        return True
    
    def _create_bot_summary(self, wizard_data: Dict, container_name: str) -> str:
        """Create formatted summary of bot configuration"""
        symbol = wizard_data.get('symbol', 'N/A')
        amount = wizard_data.get('amount', 'N/A')
        direction = wizard_data.get('direction', 'N/A')
        test_mode = wizard_data.get('test_mode', False)
        stop_loss = wizard_data.get('stop_loss')
        take_profit = wizard_data.get('take_profit')
        
        summary = f"🎯 Symbol: `{symbol}`\n"
        summary += f"💰 Amount: `{amount} USDT`\n"
        summary += f"📈 Direction: `{direction}`\n"
        summary += f"🧪 Test Mode: `{'Enabled' if test_mode else 'Disabled'}`\n"
        summary += f"📉 Stop Loss: `{stop_loss}%` {' (disabled)' if stop_loss is None else ''}\n"
        summary += f"📈 Take Profit: `{take_profit}%` {' (disabled)' if take_profit is None else ''}\n"
        summary += f"🏷️ Container: `{container_name}`"
        
        return summary
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for bot management. Returns True if handled."""
        if not data.startswith('bot_'):
            return False
        
        try:
            if data == "bot_list":
                await self._handle_callback_list(query)
            elif data == "bot_status":
                await self._handle_callback_status(query)
            elif data == "bot_stats":
                await self._handle_callback_stats(query)
            elif data == "bot_create":
                await self._handle_callback_create(query)
            elif data == "bot_start_all":
                await self._handle_callback_start_all(query)
            elif data == "bot_stop_all":
                await self._handle_callback_stop_all(query)
            elif data == "bot_confirm_stop_all":
                await self._handle_callback_confirm_stop_all(query)
            elif data == "bot_create_confirm":
                await self._handle_callback_create_confirm(query)
            elif data == "bot_create_cancel":
                await self._handle_callback_create_cancel(query)
            elif data.startswith("bot_start_"):
                container = data.replace("bot_start_", "")
                await self._handle_callback_start(query, container)
            elif data.startswith("bot_stop_"):
                container = data.replace("bot_stop_", "")
                await self._handle_callback_stop(query, container)
            elif data.startswith("bot_restart_"):
                container = data.replace("bot_restart_", "")
                await self._handle_callback_restart(query, container)
            elif data.startswith("bot_logs_"):
                container = data.replace("bot_logs_", "")
                await self._handle_callback_logs(query, container)
            elif data.startswith("bot_remove_"):
                container = data.replace("bot_remove_", "")
                await self._handle_callback_remove(query, container)
            elif data.startswith("bot_status_"):
                container = data.replace("bot_status_", "")
                await self._handle_callback_status_specific(query, container)
            elif data.startswith("bot_confirm_stop_"):
                container = data.replace("bot_confirm_stop_", "")
                await self._handle_callback_confirm_stop(query, container)
            else:
                return False
            
            return True
            
        except Exception as e:
            await query.answer(f"Error: {str(e)}")
            return True
    
    # Callback implementation methods (shortened for brevity)
    async def _handle_callback_list(self, query):
        """Handle list containers callback"""
        command = ["./bot.sh", "list"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("🔄 Refresh", "bot_list"), ("▶️ Start All", "bot_start_all")],
            [("⏹️ Stop All", "bot_stop_all"), ("📊 Stats", "bot_stats")],
            [("➕ Create New", "bot_create"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"🤖 <b>Trading Bot Containers</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_create_confirm(self, query):
        """Handle create bot confirmation"""
        user_id = query.from_user.id
        wizard_data = self.session_manager.get_wizard_data(user_id)
        
        if not wizard_data:
            await query.answer("Session expired. Please start again.")
            return
        
        # Build command from wizard data
        command = ["./bot.sh", "start"]
        command.extend([wizard_data['symbol'], wizard_data['amount']])
        command.extend([f"--direction={wizard_data['direction']}"])
        
        if wizard_data.get('test_mode'):
            command.append('--test-mode')
        
        if wizard_data.get('stop_loss'):
            command.append(f"--stop-loss={wizard_data['stop_loss']}")
        
        if wizard_data.get('take_profit'):
            command.append(f"--take-profit={wizard_data['take_profit']}")
        
        # Execute start command
        response = await self.execute_botsh_with_response(command)
        
        # Finish wizard
        self.session_manager.finish_wizard(user_id)
        
        keyboard = self.create_keyboard([
            [("📊 Status", "bot_status"), ("📋 Logs", "bot_logs_trader")],
            [("📋 List All", "bot_list"), ("➕ Create Another", "bot_create")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(
            query,
            f"🚀 <b>Bot Creation Complete</b>\n\n{response}\n\n✅ Your trading bot is now running!",
            reply_markup=keyboard
        )
    
    # Additional callback methods would be implemented here...
    # (Abbreviated for space - each would follow similar patterns)
    
    async def _handle_callback_status(self, query):
        """Handle status callback"""
        command = ["./bot.sh", "status"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("🔄 Refresh", "bot_status")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"📊 <b>Container Status</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_stats(self, query):
        """Handle stats callback"""
        command = ["./bot.sh", "stats"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("🔄 Refresh", "bot_stats")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"📊 <b>Bot Statistics</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_create(self, query):
        """Handle create new bot callback"""
        user_id = query.from_user.id
        self.session_manager.clear_session(user_id)
        
        # Check for credentials first
        returncode, stdout, stderr = await self.execute_botsh_command(["./bot.sh", "creds", "list"])
        
        if returncode != 0 or "No credentials found" in stdout:
            await self.edit_message(
                query,
                "❌ <b>No Credentials Found</b>\n\n"
                "You need to setup API credentials first.\n\n"
                "Use `/addcreds` to add credentials, then try again."
            )
            return
        
        # Start wizard
        self.session_manager.start_wizard(user_id, 'create_bot')
        
        await self.edit_message(
            query,
            "🤖 <b>Create New Trading Bot Wizard</b>\n\n"
            "Step 1/7: What symbol do you want to trade?\n\n"
            "Examples:\n"
            "• `HYPER/USDT:USDT`\n"
            "• `BTC/USDT:USDT`\n"
            "• `ETH/USDT:USDT`\n\n"
            "📝 Send next message with trading symbol:"
        )
    
    async def _handle_callback_start_all(self, query):
        """Handle start all containers callback"""
        command = ["./bot.sh", "start-all"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📊 Status", "bot_status"), ("📋 List All", "bot_list")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"▶️ <b>Start All Complete</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_stop_all(self, query):
        """Handle stop all containers callback"""
        keyboard = self.create_keyboard([
            [("✅ Yes, Stop All", "bot_confirm_stop_all"), ("❌ Cancel", "close")]
        ])
        
        await self.edit_message(
            query,
            "🛑 <b>Stop All Containers</b>\n\n"
            "Are you sure you want to stop ALL trading containers?\n\n"
            "⚠️ This will halt ALL trading activity!\n"
            "💡 Individual containers can be restarted later.",
            reply_markup=keyboard
        )
    
    async def _handle_callback_confirm_stop_all(self, query):
        """Handle confirmed stop all callback"""
        command = ["./bot.sh", "stop-all"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📊 Status", "bot_status"), ("📋 List All", "bot_list")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"⏹️ <b>Stop All Complete</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_create_cancel(self, query):
        """Handle create bot cancel callback"""
        user_id = query.from_user.id
        self.session_manager.finish_wizard(user_id)
        
        await self.edit_message(query, "❌ <b>Bot Creation Cancelled</b>\n\nWizard terminated. Use /createbot to start again.")
    
    async def _handle_callback_start(self, query, container: str):
        """Handle start specific container callback"""
        command = ["./bot.sh", "start", container]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📊 Status", f"bot_status_{container}"), ("📋 Logs", f"bot_logs_{container}")],
            [("📋 List All", "bot_list"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"▶️ <b>Start Complete: {container}</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_stop(self, query, container: str):
        """Handle stop specific container callback"""
        keyboard = self.create_keyboard([
            [("✅ Yes, Stop", f"bot_confirm_stop_{container}"), ("❌ Cancel", "close")]
        ])
        
        await self.edit_message(
            query,
            f"⏹️ <b>Confirm Stop: {container}</b>\n\n"
            f"Are you sure you want to stop container: `{container}`?\n\n"
            f"⚠️ This will halt trading activity.",
            reply_markup=keyboard
        )
    
    async def _handle_callback_restart(self, query, container: str):
        """Handle restart specific container callback"""
        command = ["./bot.sh", "restart", container]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📊 Status", f"bot_status_{container}"), ("📋 Logs", f"bot_logs_{container}")],
            [("📋 List All", "bot_list"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"🔄 <b>Restart Complete: {container}</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_logs(self, query, container: str):
        """Handle show logs callback"""
        command = ["./bot.sh", "logs", container, "50"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("🔄 Refresh", f"bot_logs_{container}"), ("📊 Status", f"bot_status_{container}")],
            [("📋 List All", "bot_list"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"📋 <b>Logs: {container}</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_remove(self, query, container: str):
        """Handle remove container callback"""
        command = ["./bot.sh", "remove", container]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("➕ Create New", "bot_create")],
            [("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"🗑️ <b>Remove Complete: {container}</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_status_specific(self, query, container: str):
        """Handle status for specific container callback"""
        command = ["./bot.sh", "status", container]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("▶️ Start", f"bot_start_{container}"), ("⏹️ Stop", f"bot_stop_{container}")],
            [("🔄 Restart", f"bot_restart_{container}"), ("📋 Logs", f"bot_logs_{container}")],
            [("🗑️ Remove", f"bot_remove_{container}"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"📊 <b>Status: {container}</b>\n\n{response}", reply_markup=keyboard)
    
    async def _handle_callback_confirm_stop(self, query, container: str):
        """Handle confirmed stop specific container callback"""
        command = ["./bot.sh", "stop", container]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("📊 Status", f"bot_status_{container}"), ("▶️ Start", f"bot_start_{container}")],
            [("📋 List All", "bot_list"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"⏹️ <b>Stop Complete: {container}</b>\n\n{response}", reply_markup=keyboard)
    
    # ===============================
    # Abstract Method Implementation
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Main command dispatcher"""
        # This is handled by individual command handlers
        pass 