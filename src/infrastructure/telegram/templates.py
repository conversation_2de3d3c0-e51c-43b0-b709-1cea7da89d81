"""Template system for Telegram messages"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass


@dataclass
class MessageTemplate:
    """Data class for message templates"""
    title: str
    content: str
    parse_mode: str = "HTML"
    keyboard: Optional[List[List[Dict[str, str]]]] = None


class TelegramTemplates:
    """Centralized template system for Telegram messages"""
    
    # HTML Formatting Methods
    @staticmethod
    def bold(text: str) -> str:
        """Format text as bold HTML"""
        return f"<b>{text}</b>"
    
    @staticmethod
    def italic(text: str) -> str:
        """Format text as italic HTML"""
        return f"<i>{text}</i>"
    
    @staticmethod
    def code(text: str) -> str:
        """Format text as inline code HTML"""
        return f"<code>{text}</code>"
    
    @staticmethod
    def pre(text: str) -> str:
        """Format text as preformatted block HTML"""
        return f"<pre>{text}</pre>"
    
    @staticmethod
    def escape_html(text: str) -> str:
        """Escape HTML special characters"""
        return text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')
    
    # Generic Templates
    @staticmethod
    def error_message(error: str, command: str = "") -> str:
        """Format error message"""
        msg = f"❌ {TelegramTemplates.bold('Error')}\n\n"
        if command:
            msg += f"Command: {TelegramTemplates.code(command)}\n"
        msg += f"Error: {error}"
        return msg
    
    @staticmethod
    def success_message(message: str, details: str = "") -> str:
        """Format success message"""
        msg = f"✅ {TelegramTemplates.bold('Success')}\n\n{message}"
        if details:
            msg += f"\n\n{TelegramTemplates.pre(details)}"
        return msg
    
    @staticmethod
    def info_message(title: str, content: str) -> str:
        """Format info message"""
        return f"ℹ️ {TelegramTemplates.bold(title)}\n\n{content}"
    
    @staticmethod
    def warning_message(message: str) -> str:
        """Format warning message"""
        return f"⚠️ {TelegramTemplates.bold('Warning')}\n\n{message}"
    
    # Credential Management Templates
    @staticmethod
    def setkey_help() -> MessageTemplate:
        """Template for /setkey help message"""
        content = f"""{TelegramTemplates.bold('Quick Setup API Credentials')}

Usage: /setkey <api_key> <api_secret>

Example:
{TelegramTemplates.code('/setkey your_api_key_here your_secret_here')}

🔐 Will be stored as 'default' profile
💡 For advanced setup with custom name: /addcreds"""
        
        return MessageTemplate(
            title="Setup API Credentials",
            content=content
        )
    
    @staticmethod
    def credentials_stored(response: str) -> str:
        """Template for successful credential storage"""
        return TelegramTemplates.success_message("Credentials stored", response)
    
    @staticmethod
    def credentials_list(profiles: List[Dict[str, Any]]) -> MessageTemplate:
        """Template for listing credentials"""
        content = f"{TelegramTemplates.bold('Stored Credential Profiles')}\n"
        content += "=" * 30 + "\n\n"
        
        if not profiles:
            content += "📭 No credential profiles found"
        else:
            for profile in profiles:
                security_icon = "🛡️" if profile.get('encrypted') else "🔓"
                content += f"• {TelegramTemplates.bold(profile['name'])}\n"
                content += f"  📝 Display: {profile['display_name']}\n"
                content += f"  📅 Created: {profile['created_at']}\n"
                content += f"  🔄 Used: {profile['usage_count']} times\n"
                content += f"  {security_icon} Security: {profile['security_level']}\n\n"
        
        keyboard = [
            [{"text": "➕ Add New", "callback_data": "creds_add"}, 
             {"text": "🔧 Manage", "callback_data": "creds_manage"}],
            [{"text": "🔄 Refresh", "callback_data": "creds_list"}, 
             {"text": "❌ Close", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Credential Profiles",
            content=content,
            keyboard=keyboard
        )
    
    @staticmethod
    def credentials_deleted(profile: str) -> str:
        """Template for credential deletion confirmation"""
        return TelegramTemplates.success_message(
            f"Credentials deleted for profile: {profile}",
            "Profile has been permanently removed"
        )
    
    @staticmethod
    def confirm_delete_credentials(profile: str) -> MessageTemplate:
        """Template for credential deletion confirmation"""
        content = f"""⚠️ {TelegramTemplates.bold('Confirm Deletion')}

Are you sure you want to delete credentials for profile: {TelegramTemplates.code(profile)}?

This action cannot be undone!"""
        
        keyboard = [
            [{"text": "✅ Yes, Delete", "callback_data": f"creds_confirm_delete_{profile}"},
             {"text": "❌ Cancel", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Confirm Deletion",
            content=content,
            keyboard=keyboard
        )
    
    # Bot Management Templates
    @staticmethod
    def bot_list(containers: List[Dict[str, Any]]) -> MessageTemplate:
        """Template for bot listing"""
        content = f"{TelegramTemplates.bold('Trading Bot Containers')}\n\n"
        
        if not containers:
            content += "📭 No trading bot containers found\n\n"
            content += "💡 Start a bot with:\n"
            content += f"   {TelegramTemplates.code('/startbot <symbol> amount=<amount>')}"
        else:
            for container in containers:
                status_emoji = "🟢" if container['status'] == 'running' else "🔴"
                content += f"{status_emoji} {TelegramTemplates.bold(container['symbol'])}\n"
                content += f"   Container: {container['name']}\n"
                content += f"   Status: {container['status']}\n"
                content += f"   Created: {container['created']}\n\n"
        
        keyboard = [
            [{"text": "🔄 Refresh", "callback_data": "bot_list"},
             {"text": "▶️ Start All", "callback_data": "bot_start_all"}],
            [{"text": "⏹️ Stop All", "callback_data": "bot_stop_all"},
             {"text": "📊 Stats", "callback_data": "bot_stats"}],
            [{"text": "➕ Create New", "callback_data": "bot_create"},
             {"text": "❌ Close", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Trading Bots",
            content=content,
            keyboard=keyboard
        )
    
    @staticmethod
    def bot_status(container_name: str, status_info: Dict[str, Any]) -> MessageTemplate:
        """Template for bot status display"""
        status_emoji = "🟢" if status_info['status'] == 'running' else "🔴"
        
        content = f"{TelegramTemplates.bold('Container Status')}\n\n"
        content += f"{status_emoji} Container: {TelegramTemplates.code(container_name)}\n"
        content += f"Status: {status_info['status']}\n"
        content += f"Image: {status_info['image']}\n"
        content += f"Created: {status_info['created']}\n"
        
        if status_info.get('config'):
            config = status_info['config']
            content += f"\n💰 {TelegramTemplates.bold('Trading Config')}:\n"
            if config.get('amount'):
                content += f"   Amount: ${config['amount']}\n"
            if config.get('direction'):
                content += f"   Direction: {config['direction']}\n"
            if config.get('test_mode'):
                content += f"   Test Mode: Enabled\n"
        
        keyboard = [
            [{"text": "▶️ Start", "callback_data": f"bot_start_{container_name}"},
             {"text": "⏹️ Stop", "callback_data": f"bot_stop_{container_name}"}],
            [{"text": "🔄 Restart", "callback_data": f"bot_restart_{container_name}"},
             {"text": "📋 Logs", "callback_data": f"bot_logs_{container_name}"}],
            [{"text": "🗑️ Remove", "callback_data": f"bot_remove_{container_name}"},
             {"text": "❌ Close", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Bot Status",
            content=content,
            keyboard=keyboard
        )
    
    @staticmethod
    def bot_logs(container_name: str, logs: str) -> MessageTemplate:
        """Template for bot logs display"""
        content = f"{TelegramTemplates.bold(f'Logs: {container_name}')}\n\n"
        content += TelegramTemplates.pre(logs)
        
        keyboard = [
            [{"text": "🔄 Refresh", "callback_data": f"bot_logs_{container_name}"},
             {"text": "📊 Status", "callback_data": f"bot_status_{container_name}"}],
            [{"text": "📋 List All", "callback_data": "bot_list"},
             {"text": "❌ Close", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Bot Logs",
            content=content,
            keyboard=keyboard
        )
    
    # Wizard Templates
    @staticmethod
    def wizard_add_credentials_step1() -> MessageTemplate:
        """Template for add credentials wizard step 1"""
        content = f"""{TelegramTemplates.bold('Add New Credentials Wizard')}

Step 1/4: What would you like to name this credential profile?

Examples: {TelegramTemplates.code('main')}, {TelegramTemplates.code('testnet')}, {TelegramTemplates.code('account2')}

🔤 Enter profile name (or /cancel to abort):"""
        
        return MessageTemplate(
            title="Add Credentials - Step 1",
            content=content
        )
    
    @staticmethod
    def wizard_add_credentials_step2(profile_name: str) -> MessageTemplate:
        """Template for add credentials wizard step 2"""
        content = f"""{TelegramTemplates.bold('Add New Credentials Wizard')}

Step 2/4: Enter your API Key

Profile: {TelegramTemplates.code(profile_name)}

🔑 Paste your API key here (or /cancel to abort):"""
        
        return MessageTemplate(
            title="Add Credentials - Step 2",
            content=content
        )
    
    @staticmethod
    def wizard_add_credentials_step3(profile_name: str) -> MessageTemplate:
        """Template for add credentials wizard step 3"""
        content = f"""{TelegramTemplates.bold('Add New Credentials Wizard')}

Step 3/4: Enter your API Secret

Profile: {TelegramTemplates.code(profile_name)}

🔐 Paste your API secret here (or /cancel to abort):"""
        
        return MessageTemplate(
            title="Add Credentials - Step 3",
            content=content
        )
    
    @staticmethod
    def wizard_add_credentials_step4(profile_name: str, display_name: str = "") -> MessageTemplate:
        """Template for add credentials wizard step 4"""
        content = f"""{TelegramTemplates.bold('Add New Credentials Wizard')}

Step 4/4: Optional display name

Profile: {TelegramTemplates.code(profile_name)}

💭 Enter a friendly display name for these credentials (or press /skip):

Examples: "Main Account", "Test Account", "Secondary"

Current: {display_name or 'Same as profile name'}"""
        
        keyboard = [
            [{"text": "⏭️ Skip", "callback_data": "wizard_skip"},
             {"text": "✅ Finish", "callback_data": "wizard_finish"}],
            [{"text": "❌ Cancel", "callback_data": "wizard_cancel"}]
        ]
        
        return MessageTemplate(
            title="Add Credentials - Step 4",
            content=content,
            keyboard=keyboard
        )
    
    @staticmethod
    def wizard_create_bot_step1() -> MessageTemplate:
        """Template for create bot wizard step 1"""
        content = f"""{TelegramTemplates.bold('Create Trading Bot Wizard')}

Step 1/3: Select credential profile

📋 Enter the name of the credential profile to use:

💡 Use {TelegramTemplates.code('/listcreds')} to see available profiles

🔤 Type profile name (or /cancel to abort):"""
        
        return MessageTemplate(
            title="Create Bot - Step 1",
            content=content
        )
    
    @staticmethod
    def wizard_create_bot_step2() -> MessageTemplate:
        """Template for create bot wizard step 2"""
        content = f"""{TelegramTemplates.bold('Create Trading Bot Wizard')}

Step 2/3: Enter symbol and amount

Format: {TelegramTemplates.code('symbol amount')}

Examples:
• {TelegramTemplates.code('hyper 50')} - Trade HYPER with $50
• {TelegramTemplates.code('btc 100')} - Trade BTC with $100
• {TelegramTemplates.code('DOGE/USDT:USDT 25')} - Full format with $25

💰 Enter symbol and amount:"""
        
        return MessageTemplate(
            title="Create Bot - Step 2",
            content=content
        )
    
    @staticmethod
    def wizard_create_bot_step3() -> MessageTemplate:
        """Template for create bot wizard step 3"""
        content = f"""{TelegramTemplates.bold('Create Trading Bot Wizard')}

Step 3/3: Optional trading settings

Configure advanced settings or use defaults:

<b>Available settings:</b>
• Direction: {TelegramTemplates.code('long')}, {TelegramTemplates.code('short')}, {TelegramTemplates.code('both')}
• Mode: {TelegramTemplates.code('test')} or {TelegramTemplates.code('live')}
• Stop Loss: {TelegramTemplates.code('sl:5')} (5% stop loss)
• Take Profit: {TelegramTemplates.code('tp:10')} (10% take profit)

<b>Examples:</b>
• {TelegramTemplates.code('long test sl:3 tp:8')} - Long position, test mode, 3% SL, 8% TP
• {TelegramTemplates.code('short live')} - Short position, live trading
• {TelegramTemplates.code('skip')} - Use default settings (long, test mode)

⚙️ Type settings or 'skip':"""
        
        return MessageTemplate(
            title="Create Bot - Step 3",
            content=content
        )
    
    @staticmethod
    def wizard_create_bot_step4(bot_config: Dict[str, Any]) -> MessageTemplate:
        """Template for create bot wizard step 4 (confirmation) - DEPRECATED: Now only 3 steps"""
        # This should not be called anymore as we now have only 3 steps
        # But keeping for backward compatibility
        return TelegramTemplates.wizard_create_bot_step3()
    
    # Help Templates
    @staticmethod
    def help_main() -> MessageTemplate:
        """Template for main help message"""
        content = f"""{TelegramTemplates.bold('AutoTrader Bot Commands')}

{TelegramTemplates.bold('🔑 Credential Management:')}
• /addcreds - Add new API credentials
• /listcreds - List stored credentials  
• /loadcreds [profile] - Load credentials
• <code>/setkey &lt;key&gt; &lt;secret&gt;</code> - Quick credential setup

{TelegramTemplates.bold('🤖 Bot Management:')}
• /createbot - Create new trading bot (wizard)
• /startbot <symbol> <amount> - Quick start bot
• /list - List all trading bots
• /status [symbol] - Get bot status
• /logs <symbol> [lines] - View bot logs
• /stop <symbol> - Stop bot
• /restart <symbol> - Restart bot

{TelegramTemplates.bold('📊 Information:')}
• /help - Show this help
• /about - About AutoTrader

💡 Most commands support both simple and interactive modes!"""
        
        keyboard = [
            [{"text": "🔑 Credentials", "callback_data": "help_credentials"},
             {"text": "🤖 Bot Commands", "callback_data": "help_bots"}],
            [{"text": "🎓 Getting Started", "callback_data": "help_getting_started"},
             {"text": "❌ Close", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Help",
            content=content,
            keyboard=keyboard
        )
    
    @staticmethod
    def help_getting_started() -> MessageTemplate:
        """Template for getting started help"""
        content = f"""{TelegramTemplates.bold('Getting Started with AutoTrader')}

{TelegramTemplates.bold('Step 1: Add Credentials')}
Use {TelegramTemplates.code('/addcreds')} to add your Bybit API credentials

{TelegramTemplates.bold('Step 2: Load Credentials')}
Use {TelegramTemplates.code('/loadcreds')} to activate your credentials

{TelegramTemplates.bold('Step 3: Create a Bot')}
Use {TelegramTemplates.code('/createbot')} for guided setup
or {TelegramTemplates.code('/startbot hyper 50')} for quick start

{TelegramTemplates.bold('Step 4: Monitor')}
Use {TelegramTemplates.code('/list')} to see all bots
Use {TelegramTemplates.code('/logs hyper')} to check bot logs

{TelegramTemplates.bold('🛡️ Security Notes:')}
• Credentials are AES-256 encrypted
• Never share your API keys
• Use testnet for learning
• Start with small amounts"""
        
        keyboard = [
            [{"text": "🔑 Add Credentials", "callback_data": "creds_add"},
             {"text": "🤖 Create Bot", "callback_data": "bot_create"}],
            [{"text": "📚 Back to Help", "callback_data": "help_main"},
             {"text": "❌ Close", "callback_data": "close"}]
        ]
        
        return MessageTemplate(
            title="Getting Started",
            content=content,
            keyboard=keyboard
        )
    
    # Command Usage Templates
    @staticmethod
    def usage_logs() -> str:
        """Template for logs command usage"""
        content = f"""{TelegramTemplates.bold('View Container Logs')}

Usage: /logs <symbol> [lines]

Examples:
• {TelegramTemplates.code('/logs hyper')} - Last 50 lines
• {TelegramTemplates.code('/logs hyper 100')} - Last 100 lines

Use /list to see available containers."""
        return content
    
    @staticmethod
    def usage_stop() -> str:
        """Template for stop command usage"""
        content = f"""{TelegramTemplates.bold('Stop Container')}

Usage: /stop <symbol>

Example: {TelegramTemplates.code('/stop hyper')}

Use /list to see running containers."""
        return content
    
    @staticmethod
    def usage_restart() -> str:
        """Template for restart command usage"""
        content = f"""{TelegramTemplates.bold('Restart Container')}

Usage: /restart <symbol>

Example: {TelegramTemplates.code('/restart hyper')}

Use /list to see available containers."""
        return content 