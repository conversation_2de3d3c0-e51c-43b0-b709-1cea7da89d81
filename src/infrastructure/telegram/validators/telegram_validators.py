"""Telegram Validators - Centralized validation logic for all Telegram modules"""
import re
import os
import json
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime


class TelegramValidators:
    """Centralized validation for all Telegram bot inputs"""
    
    @staticmethod
    def validate_bot_name(name: str) -> Tuple[bool, str]:
        """Validate bot/container name"""
        if not name:
            return False, "❌ Name cannot be empty"
        
        if len(name) < 3:
            return False, "❌ Name must be at least 3 characters long"
        
        if len(name) > 50:
            return False, "❌ Name cannot exceed 50 characters"
        
        # Allow letters, numbers, hyphens, underscores
        if not re.match(r'^[a-zA-Z0-9_-]+$', name):
            return False, "❌ Name can only contain letters, numbers, hyphens, and underscores"
        
        # Check if name already exists as Docker container
        try:
            result = subprocess.run([
                'docker', 'ps', '-a', '--format', '{{.Names}}'
            ], capture_output=True, text=True, check=True)
            
            existing_names = result.stdout.strip().split('\n')
            if name in existing_names:
                return False, f"❌ Container name `{name}` already exists"
        except Exception:
            pass  # If docker check fails, allow the name
        
        return True, "✅ Valid name"
    
    @staticmethod
    def validate_trading_symbol(symbol: str) -> Tuple[bool, str]:
        """Validate trading symbol format"""
        if not symbol:
            return False, "❌ Symbol cannot be empty"
        
        symbol = symbol.upper().strip()
        
        # Basic format validation
        if '/' not in symbol:
            return False, "❌ Symbol must contain '/' (e.g., BTC/USDT)"
        
        if len(symbol) < 6:
            return False, "❌ Symbol too short (minimum 6 characters)"
        
        if len(symbol) > 20:
            return False, "❌ Symbol too long (maximum 20 characters)"
        
        # Split and validate parts
        parts = symbol.split('/')
        if len(parts) != 2:
            return False, "❌ Symbol must have format BASE/QUOTE (e.g., BTC/USDT)"
        
        base, quote_part = parts
        
        # Validate base currency
        if len(base) < 2 or len(base) > 10:
            return False, "❌ Base currency length must be 2-10 characters"
        
        if not re.match(r'^[A-Z0-9]+$', base):
            return False, "❌ Base currency can only contain uppercase letters and numbers"
        
        # Handle futures format (QUOTE:SETTLE)
        if ':' in quote_part:
            quote_parts = quote_part.split(':')
            if len(quote_parts) != 2:
                return False, "❌ Futures format must be BASE/QUOTE:SETTLE"
            
            quote, settle = quote_parts
            
            # Validate quote and settlement currencies
            for curr, name in [(quote, "Quote"), (settle, "Settlement")]:
                if len(curr) < 2 or len(curr) > 10:
                    return False, f"❌ {name} currency length must be 2-10 characters"
                if not re.match(r'^[A-Z0-9]+$', curr):
                    return False, f"❌ {name} currency can only contain uppercase letters and numbers"
        else:
            # Spot trading format
            quote = quote_part
            if len(quote) < 2 or len(quote) > 10:
                return False, "❌ Quote currency length must be 2-10 characters"
            if not re.match(r'^[A-Z0-9]+$', quote):
                return False, "❌ Quote currency can only contain uppercase letters and numbers"
        
        return True, f"✅ Valid symbol: {symbol}"
    
    @staticmethod
    def validate_trading_amount(amount_str: str) -> Tuple[bool, str, Optional[float]]:
        """Validate trading amount"""
        if not amount_str:
            return False, "❌ Amount cannot be empty", None
        
        # Remove common prefixes/suffixes
        amount_str = amount_str.strip().replace('$', '').replace(',', '')
        
        try:
            amount = float(amount_str)
        except ValueError:
            return False, "❌ Amount must be a valid number", None
        
        if amount <= 0:
            return False, "❌ Amount must be positive", None
        
        if amount < 1:
            return False, "❌ Minimum amount is $1", None
        
        if amount > 100000:
            return False, "❌ Maximum amount is $100,000", None
        
        # Check decimal places (max 2)
        if '.' in amount_str and len(amount_str.split('.')[1]) > 2:
            return False, "❌ Amount can have maximum 2 decimal places", None
        
        return True, f"✅ Valid amount: ${amount:.2f}", amount
    
    @staticmethod
    def validate_api_credentials(creds_data: Dict) -> Tuple[bool, str]:
        """Validate API credentials structure"""
        required_fields = ['api_key', 'api_secret', 'exchange']
        
        for field in required_fields:
            if field not in creds_data or not creds_data[field]:
                return False, f"❌ Missing required field: {field}"
        
        # Validate API key format
        api_key = creds_data['api_key'].strip()
        if len(api_key) < 10:
            return False, "❌ API key too short (minimum 10 characters)"
        
        if len(api_key) > 200:
            return False, "❌ API key too long (maximum 200 characters)"
        
        # Validate API secret format
        api_secret = creds_data['api_secret'].strip()
        if len(api_secret) < 10:
            return False, "❌ API secret too short (minimum 10 characters)"
        
        if len(api_secret) > 200:
            return False, "❌ API secret too long (maximum 200 characters)"
        
        # Validate exchange
        supported_exchanges = ['bybit', 'binance', 'okx', 'bitget', 'kucoin']
        exchange = creds_data['exchange'].lower().strip()
        if exchange not in supported_exchanges:
            return False, f"❌ Unsupported exchange. Supported: {', '.join(supported_exchanges)}"
        
        # Validate optional fields
        if 'testnet' in creds_data:
            if not isinstance(creds_data['testnet'], bool):
                return False, "❌ Testnet field must be true or false"
        
        if 'display_name' in creds_data:
            display_name = creds_data['display_name'].strip()
            if len(display_name) > 50:
                return False, "❌ Display name cannot exceed 50 characters"
        
        return True, "✅ Valid credentials format"
    
    @staticmethod
    def validate_config_file(config_data: Dict) -> Tuple[bool, str]:
        """Validate trading configuration structure"""
        required_sections = ['trading', 'risk_management']
        
        for section in required_sections:
            if section not in config_data:
                return False, f"❌ Missing required section: {section}"
        
        # Validate trading section
        trading = config_data['trading']
        required_trading_fields = ['exchange', 'symbol', 'timeframe']
        
        for field in required_trading_fields:
            if field not in trading:
                return False, f"❌ Missing trading.{field}"
        
        # Validate symbol in config
        symbol_valid, symbol_msg = TelegramValidators.validate_trading_symbol(trading['symbol'])
        if not symbol_valid:
            return False, f"❌ Invalid config symbol: {symbol_msg}"
        
        # Validate timeframe
        valid_timeframes = ['1m', '3m', '5m', '15m', '30m', '1h', '2h', '4h', '6h', '8h', '12h', '1d']
        if trading['timeframe'] not in valid_timeframes:
            return False, f"❌ Invalid timeframe. Valid: {', '.join(valid_timeframes)}"
        
        # Validate risk management
        risk = config_data['risk_management']
        
        # Check leverage
        if 'leverage' in risk:
            try:
                leverage = float(risk['leverage'])
                if leverage < 1 or leverage > 100:
                    return False, "❌ Leverage must be between 1 and 100"
            except ValueError:
                return False, "❌ Leverage must be a number"
        
        # Check stop loss
        if 'stop_loss_percent' in risk:
            try:
                sl = float(risk['stop_loss_percent'])
                if sl <= 0 or sl > 50:
                    return False, "❌ Stop loss must be between 0 and 50%"
            except ValueError:
                return False, "❌ Stop loss must be a number"
        
        # Check take profit
        if 'take_profit_percent' in risk:
            try:
                tp = float(risk['take_profit_percent'])
                if tp <= 0 or tp > 1000:
                    return False, "❌ Take profit must be between 0 and 1000%"
            except ValueError:
                return False, "❌ Take profit must be a number"
        
        return True, "✅ Valid configuration structure"
    
    @staticmethod
    def validate_telegram_token(token: str) -> Tuple[bool, str]:
        """Validate Telegram bot token format"""
        if not token:
            return False, "❌ Token cannot be empty"
        
        # Telegram bot token format: 123456789:ABCdefGHIjklMNOPqrstUVWXyz
        pattern = r'^\d{8,10}:[A-Za-z0-9_-]{35}$'
        
        if not re.match(pattern, token):
            return False, "❌ Invalid token format. Should be: 123456789:ABCdefGHIjklMNOPqrstUVWXyz"
        
        return True, "✅ Valid token format"
    
    @staticmethod
    def validate_chat_id(chat_id_str: str) -> Tuple[bool, str, Optional[int]]:
        """Validate Telegram chat ID"""
        if not chat_id_str:
            return False, "❌ Chat ID cannot be empty", None
        
        try:
            chat_id = int(chat_id_str)
        except ValueError:
            return False, "❌ Chat ID must be a number", None
        
        # Chat IDs are typically large numbers
        if abs(chat_id) < 100000:
            return False, "❌ Chat ID seems too small. Please check the ID", None
        
        return True, f"✅ Valid chat ID: {chat_id}", chat_id
    
    @staticmethod
    def validate_file_path(file_path: str, must_exist: bool = False) -> Tuple[bool, str]:
        """Validate file path"""
        if not file_path:
            return False, "❌ File path cannot be empty"
        
        # Check for dangerous characters
        dangerous_chars = ['..', ';', '|', '&', '$', '`']
        for char in dangerous_chars:
            if char in file_path:
                return False, f"❌ File path contains dangerous character: {char}"
        
        if must_exist and not os.path.exists(file_path):
            return False, f"❌ File does not exist: {file_path}"
        
        # Check file extension for configs
        if file_path.endswith('.json'):
            if must_exist:
                try:
                    with open(file_path, 'r') as f:
                        json.load(f)
                except json.JSONDecodeError:
                    return False, "❌ Invalid JSON file"
                except Exception as e:
                    return False, f"❌ Cannot read file: {str(e)}"
        
        return True, "✅ Valid file path"
    
    @staticmethod
    def validate_percentage(percent_str: str, min_val: float = 0, max_val: float = 100) -> Tuple[bool, str, Optional[float]]:
        """Validate percentage input"""
        if not percent_str:
            return False, "❌ Percentage cannot be empty", None
        
        # Remove % symbol if present
        percent_str = percent_str.strip().replace('%', '')
        
        try:
            percent = float(percent_str)
        except ValueError:
            return False, "❌ Percentage must be a valid number", None
        
        if percent < min_val:
            return False, f"❌ Percentage must be at least {min_val}%", None
        
        if percent > max_val:
            return False, f"❌ Percentage cannot exceed {max_val}%", None
        
        return True, f"✅ Valid percentage: {percent}%", percent
    
    @staticmethod
    def validate_direction(direction: str) -> Tuple[bool, str]:
        """Validate trading direction"""
        if not direction:
            return False, "❌ Direction cannot be empty"
        
        valid_directions = ['long', 'short', 'both', 'long_only', 'short_only', 'both_directions']
        direction_lower = direction.lower().strip()
        
        if direction_lower not in valid_directions:
            return False, f"❌ Invalid direction. Valid: {', '.join(valid_directions)}"
        
        return True, f"✅ Valid direction: {direction}"
    
    @staticmethod
    def sanitize_filename(filename: str) -> str:
        """Sanitize filename to prevent path injection"""
        # Remove dangerous characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove path separators
        filename = filename.replace('..', '_').replace('/', '_').replace('\\', '_')
        # Remove leading/trailing spaces and dots
        filename = filename.strip(' .')
        # Ensure not empty
        if not filename:
            filename = f"file_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return filename
    
    @staticmethod
    def validate_display_name(name: str) -> Tuple[bool, str]:
        """Validate display name for credentials/configs"""
        if not name:
            return False, "❌ Display name cannot be empty"
        
        name = name.strip()
        
        if len(name) < 2:
            return False, "❌ Display name must be at least 2 characters"
        
        if len(name) > 50:
            return False, "❌ Display name cannot exceed 50 characters"
        
        # Allow letters, numbers, spaces, hyphens, underscores
        if not re.match(r'^[a-zA-Z0-9 _-]+$', name):
            return False, "❌ Display name can only contain letters, numbers, spaces, hyphens, and underscores"
        
        return True, f"✅ Valid display name: {name}"


class ValidationMessages:
    """Standard validation messages for consistency"""
    
    EMPTY_INPUT = "❌ This field cannot be empty"
    INVALID_NUMBER = "❌ Please enter a valid number"
    INVALID_FORMAT = "❌ Invalid format"
    TOO_SHORT = "❌ Input too short"
    TOO_LONG = "❌ Input too long"
    
    SUCCESS = "✅ Valid input"
    
    @staticmethod
    def length_error(min_len: int, max_len: int) -> str:
        return f"❌ Length must be between {min_len} and {max_len} characters"
    
    @staticmethod
    def range_error(min_val: float, max_val: float) -> str:
        return f"❌ Value must be between {min_val} and {max_val}"
    
    @staticmethod
    def format_error(expected_format: str) -> str:
        return f"❌ Expected format: {expected_format}"


class InputSanitizer:
    """Sanitize user inputs for security"""
    
    @staticmethod
    def sanitize_text(text: str) -> str:
        """Sanitize general text input"""
        if not text:
            return ""
        
        # Remove potential HTML/script tags
        text = re.sub(r'<[^>]*>', '', text)
        # Remove potential command injection characters
        dangerous_chars = [';', '|', '&', '$', '`', '$(', '${']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        return text.strip()
    
    @staticmethod
    def sanitize_json_string(text: str) -> str:
        """Sanitize text for JSON storage"""
        if not text:
            return ""
        
        # Escape JSON special characters
        text = text.replace('\\', '\\\\')
        text = text.replace('"', '\\"')
        text = text.replace('\n', '\\n')
        text = text.replace('\r', '\\r')
        text = text.replace('\t', '\\t')
        
        return text.strip()
    
    @staticmethod
    def sanitize_command_arg(arg: str) -> str:
        """Sanitize command line arguments"""
        if not arg:
            return ""
        
        # Remove shell metacharacters
        dangerous_chars = [';', '|', '&', '$', '`', '(', ')', '<', '>', '"', "'", '\\']
        for char in dangerous_chars:
            arg = arg.replace(char, '')
        
        return arg.strip()


# Quick access validators for common use cases
def quick_validate_bot_name(name: str) -> bool:
    """Quick boolean validation for bot name"""
    valid, _ = TelegramValidators.validate_bot_name(name)
    return valid

def quick_validate_symbol(symbol: str) -> bool:
    """Quick boolean validation for trading symbol"""
    valid, _ = TelegramValidators.validate_trading_symbol(symbol)
    return valid

def quick_validate_amount(amount_str: str) -> bool:
    """Quick boolean validation for trading amount"""
    valid, _, _ = TelegramValidators.validate_trading_amount(amount_str)
    return valid 