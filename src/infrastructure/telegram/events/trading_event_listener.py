"""Trading Event Listener - Monitor and capture trading events from exchange"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Any, Callable
from datetime import datetime
from dataclasses import dataclass
from enum import Enum

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False


class EventType(Enum):
    """Trading event types"""
    ORDER_FILLED = "order_filled"
    ORDER_PARTIAL = "order_partial"
    ORDER_CANCELLED = "order_cancelled"
    STOP_LOSS_TRIGGERED = "stop_loss_triggered"
    TAKE_PROFIT_TRIGGERED = "take_profit_triggered"
    POSITION_OPENED = "position_opened"
    POSITION_CLOSED = "position_closed"
    POSITION_LIQUIDATED = "position_liquidated"
    BALANCE_UPDATE = "balance_update"
    PRICE_ALERT = "price_alert"


@dataclass
class TradingEvent:
    """Trading event data structure"""
    event_type: EventType
    symbol: str
    timestamp: datetime
    data: Dict[str, Any]
    bot_name: str = "Unknown"
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            'event_type': self.event_type.value,
            'symbol': self.symbol,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'bot_name': self.bot_name,
            'user_id': self.user_id
        }


class ExchangeWebSocketHandler:
    """Handle WebSocket connections to exchange"""
    
    def __init__(self, exchange_name: str):
        self.exchange_name = exchange_name
        self.logger = logging.getLogger(f'WSHandler_{exchange_name}')
        self.connections: Dict[str, Any] = {}
        self.event_callbacks: List[Callable] = []
        self.is_running = False
        
    async def connect_bybit(self, symbols: List[str]):
        """Connect to Bybit WebSocket for order updates"""
        try:
            if not WEBSOCKETS_AVAILABLE:
                self.logger.error("websockets library not available")
                return
            
            # Bybit testnet WebSocket URL
            url = "wss://stream-testnet.bybit.com/v5/private"
            
            # This is a simplified connection - in real implementation,
            # you'd need proper authentication
            self.logger.info(f"Connecting to Bybit WebSocket: {url}")
            
            # Store connection info
            self.connections['bybit'] = {
                'url': url,
                'symbols': symbols,
                'status': 'connecting'
            }
            
            # Simulate WebSocket events for demo
            asyncio.create_task(self._simulate_bybit_events(symbols))
            
        except Exception as e:
            self.logger.error(f"Error connecting to Bybit: {e}")
    
    async def connect_binance(self, symbols: List[str]):
        """Connect to Binance WebSocket for order updates"""
        try:
            if not WEBSOCKETS_AVAILABLE:
                self.logger.error("websockets library not available")
                return
            
            # Binance testnet WebSocket URL
            url = "wss://testnet.binance.vision/ws-api/v3"
            
            self.logger.info(f"Connecting to Binance WebSocket: {url}")
            
            self.connections['binance'] = {
                'url': url,
                'symbols': symbols,
                'status': 'connecting'
            }
            
            # Simulate WebSocket events for demo
            asyncio.create_task(self._simulate_binance_events(symbols))
            
        except Exception as e:
            self.logger.error(f"Error connecting to Binance: {e}")
    
    async def _simulate_bybit_events(self, symbols: List[str]):
        """Simulate Bybit trading events for demo purposes"""
        await asyncio.sleep(2)  # Wait for connection
        
        self.connections['bybit']['status'] = 'connected'
        self.logger.info("Bybit WebSocket simulation started")
        
        # Simulate various trading events
        events = [
            {
                'type': EventType.ORDER_FILLED,
                'symbol': 'HYPER/USDT:USDT',
                'data': {
                    'order_id': 'BYB_12345',
                    'side': 'Buy',
                    'qty': '10',
                    'price': '0.045',
                    'executed_qty': '10',
                    'fee': '0.0002',
                    'commission_asset': 'USDT'
                }
            },
            {
                'type': EventType.TAKE_PROFIT_TRIGGERED,
                'symbol': 'BTC/USDT:USDT',
                'data': {
                    'order_id': 'BYB_67890',
                    'trigger_price': '45000',
                    'executed_price': '45050',
                    'qty': '0.001',
                    'profit': '12.50'
                }
            },
            {
                'type': EventType.STOP_LOSS_TRIGGERED,
                'symbol': 'ETH/USDT:USDT',
                'data': {
                    'order_id': 'BYB_11111',
                    'trigger_price': '2800',
                    'executed_price': '2795',
                    'qty': '0.1',
                    'loss': '-5.00'
                }
            }
        ]
        
        # Send events every 30 seconds
        for i in range(5):  # Send 5 demo events
            for event_data in events:
                if any(symbol in event_data['symbol'] for symbol in symbols):
                    trading_event = TradingEvent(
                        event_type=event_data['type'],
                        symbol=event_data['symbol'],
                        timestamp=datetime.now(),
                        data=event_data['data'],
                        bot_name='Demo_Bybit_Bot'
                    )
                    
                    await self._emit_event(trading_event)
            
            await asyncio.sleep(30)  # Wait 30 seconds between events
    
    async def _simulate_binance_events(self, symbols: List[str]):
        """Simulate Binance trading events for demo purposes"""
        await asyncio.sleep(2)
        
        self.connections['binance']['status'] = 'connected'
        self.logger.info("Binance WebSocket simulation started")
        
        # Similar simulation for Binance
        events = [
            {
                'type': EventType.POSITION_OPENED,
                'symbol': 'BTCUSDT',
                'data': {
                    'position_id': 'BIN_POS_001',
                    'side': 'LONG',
                    'size': '0.001',
                    'entry_price': '44000',
                    'margin': '100'
                }
            },
            {
                'type': EventType.BALANCE_UPDATE,
                'symbol': 'USDT',
                'data': {
                    'asset': 'USDT',
                    'free': '1000.50',
                    'locked': '50.00',
                    'change': '+25.75'
                }
            }
        ]
        
        for i in range(3):
            for event_data in events:
                trading_event = TradingEvent(
                    event_type=event_data['type'],
                    symbol=event_data['symbol'],
                    timestamp=datetime.now(),
                    data=event_data['data'],
                    bot_name='Demo_Binance_Bot'
                )
                
                await self._emit_event(trading_event)
            
            await asyncio.sleep(45)
    
    async def _emit_event(self, event: TradingEvent):
        """Emit trading event to all registered callbacks"""
        self.logger.info(f"Trading Event: {event.event_type.value} - {event.symbol}")
        
        for callback in self.event_callbacks:
            try:
                await callback(event)
            except Exception as e:
                self.logger.error(f"Error in event callback: {e}")
    
    def add_event_callback(self, callback: Callable):
        """Add callback function for trading events"""
        self.event_callbacks.append(callback)
    
    def remove_event_callback(self, callback: Callable):
        """Remove callback function"""
        if callback in self.event_callbacks:
            self.event_callbacks.remove(callback)
    
    async def disconnect(self):
        """Disconnect all WebSocket connections"""
        self.is_running = False
        for exchange, connection in self.connections.items():
            connection['status'] = 'disconnected'
            self.logger.info(f"Disconnected from {exchange}")


class TradingEventListener:
    """Main trading event listener coordinator"""
    
    def __init__(self):
        self.logger = logging.getLogger('TradingEventListener')
        self.exchanges: Dict[str, ExchangeWebSocketHandler] = {}
        self.event_filters: Dict[str, Callable] = {}
        self.is_running = False
        
        # Statistics
        self.events_received = 0
        self.events_processed = 0
        self.events_filtered = 0
        
    def add_exchange(self, exchange_name: str) -> ExchangeWebSocketHandler:
        """Add exchange WebSocket handler"""
        if exchange_name not in self.exchanges:
            handler = ExchangeWebSocketHandler(exchange_name)
            self.exchanges[exchange_name] = handler
            
            # Add event processing callback
            handler.add_event_callback(self._process_event)
            
            self.logger.info(f"Added exchange handler: {exchange_name}")
            
        return self.exchanges[exchange_name]
    
    async def start_monitoring(self, exchange_configs: Dict[str, List[str]]):
        """Start monitoring trading events from configured exchanges"""
        try:
            self.is_running = True
            self.logger.info("Starting trading event monitoring...")
            
            # Start monitoring each exchange
            for exchange_name, symbols in exchange_configs.items():
                handler = self.add_exchange(exchange_name)
                
                if exchange_name.lower() == 'bybit':
                    await handler.connect_bybit(symbols)
                elif exchange_name.lower() == 'binance':
                    await handler.connect_binance(symbols)
                else:
                    self.logger.warning(f"Unknown exchange: {exchange_name}")
            
            self.logger.info(f"Monitoring {len(exchange_configs)} exchanges")
            
        except Exception as e:
            self.logger.error(f"Error starting event monitoring: {e}")
    
    async def stop_monitoring(self):
        """Stop all event monitoring"""
        try:
            self.is_running = False
            
            for handler in self.exchanges.values():
                await handler.disconnect()
            
            self.logger.info("Stopped trading event monitoring")
            
        except Exception as e:
            self.logger.error(f"Error stopping event monitoring: {e}")
    
    async def _process_event(self, event: TradingEvent):
        """Process incoming trading event"""
        try:
            self.events_received += 1
            
            # Apply filters
            if await self._apply_filters(event):
                self.events_filtered += 1
                return
            
            self.events_processed += 1
            
            # Log event details
            self._log_event(event)
            
            # Additional processing can be added here
            # e.g., save to database, trigger alerts, etc.
            
        except Exception as e:
            self.logger.error(f"Error processing event: {e}")
    
    async def _apply_filters(self, event: TradingEvent) -> bool:
        """Apply event filters. Returns True if event should be filtered out"""
        for filter_name, filter_func in self.event_filters.items():
            try:
                if await filter_func(event):
                    self.logger.debug(f"Event filtered by {filter_name}: {event.event_type.value}")
                    return True
            except Exception as e:
                self.logger.error(f"Error in filter {filter_name}: {e}")
        
        return False
    
    def _log_event(self, event: TradingEvent):
        """Log trading event details"""
        event_msg = (
            f"📊 TRADING EVENT: {event.event_type.value.upper()}\n"
            f"Symbol: {event.symbol}\n"
            f"Bot: {event.bot_name}\n"
            f"Time: {event.timestamp.strftime('%H:%M:%S')}\n"
            f"Data: {json.dumps(event.data, indent=2)}"
        )
        self.logger.info(event_msg)
    
    def add_event_filter(self, name: str, filter_func: Callable):
        """Add event filter function"""
        self.event_filters[name] = filter_func
        self.logger.info(f"Added event filter: {name}")
    
    def remove_event_filter(self, name: str):
        """Remove event filter"""
        if name in self.event_filters:
            del self.event_filters[name]
            self.logger.info(f"Removed event filter: {name}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get event processing statistics"""
        return {
            'events_received': self.events_received,
            'events_processed': self.events_processed,
            'events_filtered': self.events_filtered,
            'active_exchanges': len(self.exchanges),
            'exchange_status': {
                name: handler.connections for name, handler in self.exchanges.items()
            },
            'filter_count': len(self.event_filters)
        }
    
    def get_connection_status(self) -> Dict[str, str]:
        """Get connection status for all exchanges"""
        status = {}
        for exchange_name, handler in self.exchanges.items():
            if handler.connections:
                # Get first connection status as representative
                first_conn = next(iter(handler.connections.values()))
                status[exchange_name] = first_conn.get('status', 'unknown')
            else:
                status[exchange_name] = 'not_connected'
        
        return status


# Example filter functions
async def filter_small_orders(event: TradingEvent) -> bool:
    """Filter out small orders below threshold"""
    if event.event_type in [EventType.ORDER_FILLED, EventType.ORDER_PARTIAL]:
        qty = float(event.data.get('qty', 0))
        price = float(event.data.get('price', 0))
        value = qty * price
        
        # Filter orders below $10 value
        return value < 10.0
    
    return False


async def filter_test_symbols(event: TradingEvent) -> bool:
    """Filter out test symbols"""
    test_symbols = ['TEST', 'DEMO', 'SANDBOX']
    return any(test in event.symbol.upper() for test in test_symbols)


# Pre-defined filter configurations
DEFAULT_FILTERS = {
    'small_orders': filter_small_orders,
    'test_symbols': filter_test_symbols
} 