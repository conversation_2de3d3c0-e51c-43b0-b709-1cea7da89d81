"""Exchange Event Handler - Process trading events and integrate with notification system"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from .trading_event_listener import TradingEventListener, TradingEvent, EventType, DEFAULT_FILTERS
from ..telegram_notification_manager import TelegramNotificationManager


class ExchangeEventHandler:
    """Handles trading events and integrates with Telegram notifications"""
    
    def __init__(self, notification_manager: TelegramNotificationManager, bot=None):
        self.logger = logging.getLogger('ExchangeEventHandler')
        self.notification_manager = notification_manager
        self.bot = bot  # Telegram bot instance
        self.event_listener = TradingEventListener()
        
        # Event processing configuration
        self.auto_notify_events = {
            EventType.ORDER_FILLED,
            EventType.TAKE_PROFIT_TRIGGERED,
            EventType.STOP_LOSS_TRIGGERED,
            EventType.POSITION_LIQUIDATED
        }
        
        # Performance tracking
        self.profit_loss_tracker: Dict[str, float] = {}
        self.daily_stats: Dict[str, Dict] = {}
        
        # Setup event listener callback
        self.event_listener.add_exchange('bybit')
        self.event_listener.add_exchange('binance')
        
        # Add default filters
        for filter_name, filter_func in DEFAULT_FILTERS.items():
            self.event_listener.add_event_filter(filter_name, filter_func)
        
        # Register our event processor
        for exchange_name, handler in self.event_listener.exchanges.items():
            handler.add_event_callback(self._handle_trading_event)
    
    async def start_monitoring(self, exchange_configs: Dict[str, List[str]]):
        """Start monitoring trading events from exchanges"""
        try:
            self.logger.info("Starting Exchange Event Handler...")
            
            # Start the event listener
            await self.event_listener.start_monitoring(exchange_configs)
            
            # Start performance tracking
            asyncio.create_task(self._performance_tracker())
            
            self.logger.info("✅ Exchange Event Handler started")
            
        except Exception as e:
            self.logger.error(f"Error starting Exchange Event Handler: {e}")
    
    async def stop_monitoring(self):
        """Stop monitoring trading events"""
        try:
            await self.event_listener.stop_monitoring()
            self.logger.info("✅ Exchange Event Handler stopped")
        except Exception as e:
            self.logger.error(f"Error stopping Exchange Event Handler: {e}")
    
    async def _handle_trading_event(self, event: TradingEvent):
        """Main event handler - processes all trading events"""
        try:
            self.logger.info(f"Processing event: {event.event_type.value} - {event.symbol}")
            
            # Update performance tracking
            await self._update_performance_tracking(event)
            
            # Check if this event should trigger auto-notifications
            if event.event_type in self.auto_notify_events:
                await self._send_event_notification(event)
            
            # Handle specific event types
            await self._handle_specific_event(event)
            
        except Exception as e:
            self.logger.error(f"Error handling trading event: {e}")
    
    async def _send_event_notification(self, event: TradingEvent):
        """Send notification for trading event"""
        try:
            if not self.bot:
                self.logger.warning("Bot instance not available")
                return
            
            notification_data = self._format_event_notification(event)
            
            if event.event_type == EventType.ORDER_FILLED:
                await self.notification_manager.broadcast_trade_alert(
                    self.bot, notification_data
                )
            elif event.event_type in [EventType.TAKE_PROFIT_TRIGGERED, EventType.STOP_LOSS_TRIGGERED]:
                await self._send_tp_sl_alert(event, notification_data)
            elif event.event_type == EventType.POSITION_LIQUIDATED:
                await self._send_liquidation_alert(event, notification_data)
                
        except Exception as e:
            self.logger.error(f"Error sending event notification: {e}")
    
    def _format_event_notification(self, event: TradingEvent) -> Dict[str, Any]:
        """Format trading event for notification"""
        base_data = {
            'symbol': event.symbol,
            'timestamp': event.timestamp.strftime('%H:%M:%S'),
            'bot_name': event.bot_name
        }
        
        if event.event_type == EventType.ORDER_FILLED:
            return {
                **base_data,
                'side': event.data.get('side', 'Unknown'),
                'amount': event.data.get('qty', '0'),
                'price': event.data.get('price', '0')
            }
        elif event.event_type == EventType.TAKE_PROFIT_TRIGGERED:
            return {
                **base_data,
                'trigger_price': event.data.get('trigger_price', '0'),
                'executed_price': event.data.get('executed_price', '0'),
                'profit': event.data.get('profit', '0')
            }
        elif event.event_type == EventType.STOP_LOSS_TRIGGERED:
            return {
                **base_data,
                'trigger_price': event.data.get('trigger_price', '0'),
                'executed_price': event.data.get('executed_price', '0'),
                'loss': event.data.get('loss', '0')
            }
        
        return base_data
    
    async def _send_tp_sl_alert(self, event: TradingEvent, notification_data: Dict):
        """Send Take Profit or Stop Loss alert"""
        try:
            if not self.bot:
                self.logger.warning("Bot instance not available for TP/SL alerts")
                return
                
            if event.event_type == EventType.TAKE_PROFIT_TRIGGERED:
                message = self._format_take_profit_message(event, notification_data)
                emoji = "🎉"
            else:  # STOP_LOSS_TRIGGERED
                message = self._format_stop_loss_message(event, notification_data)
                emoji = "⚠️"
            
            # Send to all subscribers
            for chat_id, preferences in self.notification_manager.subscribers.items():
                if preferences.get('trade_alerts', False):
                    try:
                        await self.bot.send_message(
                            chat_id=chat_id,
                            text=message,
                            parse_mode='Markdown'
                        )
                    except Exception as e:
                        self.logger.error(f"Failed to send TP/SL alert to {chat_id}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error sending TP/SL alert: {e}")
    
    async def _send_liquidation_alert(self, event: TradingEvent, notification_data: Dict):
        """Send liquidation alert"""
        try:
            if not self.bot:
                self.logger.warning("Bot instance not available for liquidation alerts")
                return
                
            message = (
                f"🚨 **LIQUIDATION ALERT** 🚨\n\n"
                f"Symbol: `{event.symbol}`\n"
                f"Bot: `{event.bot_name}`\n"
                f"Time: `{notification_data['timestamp']}`\n\n"
                f"⚠️ **Position was liquidated!**\n"
                f"Please check your risk management settings."
            )
            
            # Send to all subscribers (liquidation is critical)
            for chat_id in self.notification_manager.subscribers.keys():
                try:
                    await self.bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    self.logger.error(f"Failed to send liquidation alert to {chat_id}: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error sending liquidation alert: {e}")
    
    def _format_take_profit_message(self, event: TradingEvent, data: Dict) -> str:
        """Format take profit notification message"""
        profit = data.get('profit', '0')
        return (
            f"🎉 **TAKE PROFIT TRIGGERED** 🎉\n\n"
            f"Symbol: `{event.symbol}`\n"
            f"Bot: `{event.bot_name}`\n"
            f"Trigger Price: `{data.get('trigger_price', 'N/A')}`\n"
            f"Executed Price: `{data.get('executed_price', 'N/A')}`\n"
            f"💰 **Profit: +${profit}**\n"
            f"Time: `{data['timestamp']}`\n\n"
            f"🚀 Great trade! Keep it up!"
        )
    
    def _format_stop_loss_message(self, event: TradingEvent, data: Dict) -> str:
        """Format stop loss notification message"""
        loss = data.get('loss', '0')
        return (
            f"⚠️ **STOP LOSS TRIGGERED** ⚠️\n\n"
            f"Symbol: `{event.symbol}`\n"
            f"Bot: `{event.bot_name}`\n"
            f"Trigger Price: `{data.get('trigger_price', 'N/A')}`\n"
            f"Executed Price: `{data.get('executed_price', 'N/A')}`\n"
            f"📉 **Loss: {loss}**\n"
            f"Time: `{data['timestamp']}`\n\n"
            f"💡 Risk management working as intended."
        )
    
    async def _handle_specific_event(self, event: TradingEvent):
        """Handle specific event types with custom logic"""
        try:
            if event.event_type == EventType.POSITION_OPENED:
                await self._handle_position_opened(event)
            elif event.event_type == EventType.POSITION_CLOSED:
                await self._handle_position_closed(event)
            elif event.event_type == EventType.BALANCE_UPDATE:
                await self._handle_balance_update(event)
            elif event.event_type == EventType.ORDER_CANCELLED:
                await self._handle_order_cancelled(event)
                
        except Exception as e:
            self.logger.error(f"Error in specific event handler: {e}")
    
    async def _handle_position_opened(self, event: TradingEvent):
        """Handle position opened event"""
        position_size = event.data.get('size', 0)
        entry_price = event.data.get('entry_price', 0)
        
        self.logger.info(f"Position opened: {event.symbol} - Size: {position_size} @ {entry_price}")
        
        # Track position for performance monitoring
        position_key = f"{event.bot_name}_{event.symbol}"
        self.profit_loss_tracker[position_key] = 0.0
    
    async def _handle_position_closed(self, event: TradingEvent):
        """Handle position closed event"""
        realized_pnl = event.data.get('realized_pnl', 0)
        
        self.logger.info(f"Position closed: {event.symbol} - PnL: {realized_pnl}")
        
        # Update daily stats
        today = datetime.now().strftime('%Y-%m-%d')
        if today not in self.daily_stats:
            self.daily_stats[today] = {'trades': 0, 'profit': 0.0, 'loss': 0.0}
        
        self.daily_stats[today]['trades'] += 1
        if float(realized_pnl) > 0:
            self.daily_stats[today]['profit'] += float(realized_pnl)
        else:
            self.daily_stats[today]['loss'] += abs(float(realized_pnl))
    
    async def _handle_balance_update(self, event: TradingEvent):
        """Handle balance update event"""
        asset = event.data.get('asset', 'Unknown')
        change = event.data.get('change', '0')
        
        self.logger.info(f"Balance update: {asset} - Change: {change}")
        
        # Check for significant balance changes
        try:
            change_amount = float(change.replace('+', ''))
            if abs(change_amount) > 100:  # Alert for changes > $100
                await self._send_balance_alert(event, change_amount)
        except ValueError:
            pass  # Invalid change format
    
    async def _handle_order_cancelled(self, event: TradingEvent):
        """Handle order cancelled event"""
        order_id = event.data.get('order_id', 'Unknown')
        reason = event.data.get('reason', 'User cancelled')
        
        self.logger.info(f"Order cancelled: {order_id} - Reason: {reason}")
    
    async def _send_balance_alert(self, event: TradingEvent, change_amount: float):
        """Send balance change alert for significant amounts"""
        try:
            emoji = "📈" if change_amount > 0 else "📉"
            message = (
                f"{emoji} **BALANCE UPDATE** {emoji}\n\n"
                f"Asset: `{event.data.get('asset', 'Unknown')}`\n"
                f"Change: `${change_amount:+.2f}`\n"
                f"Free: `{event.data.get('free', 'N/A')}`\n"
                f"Locked: `{event.data.get('locked', 'N/A')}`\n"
                f"Time: `{event.timestamp.strftime('%H:%M:%S')}`"
            )
            
            # Send to subscribers with balance alerts enabled
            for chat_id, preferences in self.notification_manager.subscribers.items():
                if preferences.get('profit_loss', False):
                    try:
                        if self.bot:
                            await self.bot.send_message(
                                chat_id=chat_id,
                                text=message,
                                parse_mode='Markdown'
                            )
                    except Exception as e:
                        self.logger.error(f"Failed to send balance alert to {chat_id}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error sending balance alert: {e}")
    
    async def _update_performance_tracking(self, event: TradingEvent):
        """Update performance tracking data"""
        try:
            position_key = f"{event.bot_name}_{event.symbol}"
            
            if event.event_type == EventType.TAKE_PROFIT_TRIGGERED:
                profit = float(event.data.get('profit', 0))
                self.profit_loss_tracker[position_key] = profit
                
            elif event.event_type == EventType.STOP_LOSS_TRIGGERED:
                loss = float(event.data.get('loss', 0))
                self.profit_loss_tracker[position_key] = loss
                
        except Exception as e:
            self.logger.error(f"Error updating performance tracking: {e}")
    
    async def _performance_tracker(self):
        """Background task to track and report performance"""
        while True:
            try:
                await asyncio.sleep(3600)  # Check every hour
                await self._generate_performance_report()
            except Exception as e:
                self.logger.error(f"Error in performance tracker: {e}")
                await asyncio.sleep(3600)
    
    async def _generate_performance_report(self):
        """Generate and send performance report"""
        try:
            today = datetime.now().strftime('%Y-%m-%d')
            stats = self.daily_stats.get(today, {'trades': 0, 'profit': 0.0, 'loss': 0.0})
            
            if stats['trades'] == 0:
                return  # No trades today
            
            net_pnl = stats['profit'] - stats['loss']
            win_rate = (stats['profit'] / (stats['profit'] + stats['loss']) * 100) if (stats['profit'] + stats['loss']) > 0 else 0
            
            message = (
                f"📊 **Daily Performance Report** 📊\n\n"
                f"Date: `{today}`\n"
                f"Total Trades: `{stats['trades']}`\n"
                f"Profit: `+${stats['profit']:.2f}`\n"
                f"Loss: `-${stats['loss']:.2f}`\n"
                f"Net P&L: `${net_pnl:+.2f}`\n"
                f"Win Rate: `{win_rate:.1f}%`\n\n"
                f"🔄 Keep trading smart!"
            )
            
            # Send to subscribers with daily reports enabled
            for chat_id, preferences in self.notification_manager.subscribers.items():
                if preferences.get('profit_loss', False):
                    try:
                        if self.bot:
                            await self.bot.send_message(
                                chat_id=chat_id,
                                text=message,
                                parse_mode='Markdown'
                            )
                    except Exception as e:
                        self.logger.error(f"Failed to send performance report to {chat_id}: {e}")
                        
        except Exception as e:
            self.logger.error(f"Error generating performance report: {e}")
    
    def get_event_statistics(self) -> Dict[str, Any]:
        """Get comprehensive event statistics"""
        listener_stats = self.event_listener.get_statistics()
        connection_status = self.event_listener.get_connection_status()
        
        today = datetime.now().strftime('%Y-%m-%d')
        daily_stats = self.daily_stats.get(today, {'trades': 0, 'profit': 0.0, 'loss': 0.0})
        
        return {
            'event_listener': listener_stats,
            'connections': connection_status,
            'daily_performance': daily_stats,
            'active_positions': len(self.profit_loss_tracker),
            'auto_notify_events': [event.value for event in self.auto_notify_events],
            'notification_subscribers': len(self.notification_manager.subscribers)
        }
    
    def configure_auto_notifications(self, event_types: List[EventType]):
        """Configure which events trigger automatic notifications"""
        self.auto_notify_events = set(event_types)
        self.logger.info(f"Auto notifications configured for: {[e.value for e in event_types]}")
    
    def add_custom_filter(self, name: str, filter_func):
        """Add custom event filter"""
        self.event_listener.add_event_filter(name, filter_func)
    
    def remove_custom_filter(self, name: str):
        """Remove custom event filter"""
        self.event_listener.remove_event_filter(name) 