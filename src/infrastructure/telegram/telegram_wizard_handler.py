"""Telegram Wizard Handler - Manage multi-step wizards and operations"""
from typing import Dict, List, Optional, Any
from .telegram_base import Teleg<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, UserSessionManager

try:
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TelegramWizardHandler(TelegramBaseHandler):
    """Handles wizard flows and multi-step operations"""
    
    def __init__(self, session_manager: UserSessionManager):
        super().__init__('TelegramWizardHandler')
        self.session_manager = session_manager
    
    # ===============================
    # Wizard Management
    # ===============================
    
    async def handle_cancel_command(self, update, context):
        """Handle /cancel command - Cancel any active wizard"""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "ℹ️ No active wizard to cancel."
            )
            return
        
        session = self.session_manager.get_session(user_id)
        wizard_type = session.get('wizard_state', 'Unknown')
        
        self.session_manager.finish_wizard(user_id)
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            f"❌ <b>Wizard Cancelled</b>\n\n"
            f"Operation: `{wizard_type}`\n"
            f"You can start a new operation anytime."
        )
    
    async def handle_status_wizard_command(self, update, context):
        """Handle /wizardstatus command - Show active wizard status"""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "ℹ️ <b>No Active Wizard</b>\n\n"
                "Available wizards:\n"
                "• `/addcreds` - Add credentials\n"
                "• `/createbot` - Create trading bot\n"
                "• `/createconfig` - Create config file"
            )
            return
        
        session = self.session_manager.get_session(user_id)
        wizard_type = session.get('wizard_state')
        wizard_data = session.get('wizard_data', {})
        
        status_msg = f"🧙‍♂️ <b>Active Wizard</b>\n\n"
        status_msg += f"Type: `{wizard_type}`\n"
        status_msg += f"Progress: {len(wizard_data)} steps completed\n\n"
        
        if wizard_data:
            status_msg += "<b>Current Data:</b>\n"
            for key, value in wizard_data.items():
                if 'secret' in key.lower() or 'password' in key.lower():
                    value = "*<b>hidden</b>*"
                status_msg += f"• {key}: `{value}`\n"
        
        status_msg += f"\n💡 Use `/cancel` to abort wizard"
        
        keyboard = self.create_keyboard([
            [("❌ Cancel Wizard", "wizard_cancel"), ("ℹ️ Help", "wizard_help")]
        ])
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            status_msg,
            reply_markup=keyboard
        )
    
    # ===============================
    # Wizard Router
    # ===============================
    
    async def route_wizard_input(self, update, context, handlers: List) -> bool:
        """Route wizard input to appropriate handler. Returns True if handled."""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            return False
        
        # Try each handler until one handles the input
        for handler in handlers:
            if hasattr(handler, 'handle_wizard_input'):
                if await handler.handle_wizard_input(update, context):
                    return True
        
        return False
    
    async def handle_wizard_text_input(self, update, context, handlers: List):
        """Main text input handler for all wizards"""
        user_id = update.effective_user.id
        text = update.message.text.strip()
        
        # Check for global wizard commands
        if text.lower() in ['/cancel', 'cancel', 'abort', 'quit']:
            await self.handle_cancel_command(update, context)
            return
        
        # Route to specific wizard handlers
        handled = await self.route_wizard_input(update, context, handlers)
        
        if not handled:
            session = self.session_manager.get_session(user_id)
            wizard_type = session.get('wizard_state', 'Unknown')
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"❓ <b>Unrecognized Input</b>\n\n"
                f"Active wizard: `{wizard_type}`\n"
                f"Your input: `{text}`\n\n"
                f"💡 Use `/cancel` to abort wizard\n"
                f"💡 Use `/wizardstatus` for current status"
            )
    
    # ===============================
    # Wizard Utilities
    # ===============================
    
    def get_wizard_progress(self, user_id: int, total_steps: int) -> str:
        """Get wizard progress string"""
        wizard_data = self.session_manager.get_wizard_data(user_id)
        current_step = len(wizard_data) + 1
        
        progress_bar = "▓" * current_step + "░" * (total_steps - current_step)
        return f"Progress: [{progress_bar}] {current_step}/{total_steps}"
    
    def validate_wizard_step(self, user_id: int, required_keys: List[str]) -> bool:
        """Validate if wizard has required data for current step"""
        wizard_data = self.session_manager.get_wizard_data(user_id)
        return all(key in wizard_data for key in required_keys)
    
    def get_next_step_message(self, wizard_type: str, current_step: int) -> str:
        """Get next step message for wizard type"""
        messages = {
            'add_credentials': {
                1: "Step 1/4: Profile name",
                2: "Step 2/4: API Key", 
                3: "Step 3/4: API Secret",
                4: "Step 4/4: Display name"
            },
            'create_bot': {
                1: "Step 1/7: Trading symbol",
                2: "Step 2/7: Amount",
                3: "Step 3/7: Direction", 
                4: "Step 4/7: Test mode",
                5: "Step 5/7: Stop loss",
                6: "Step 6/7: Take profit",
                7: "Step 7/7: Container name"
            },
            'create_config': {
                1: "Step 1/6: Config name",
                2: "Step 2/6: Trading symbol",
                3: "Step 3/6: Amount",
                4: "Step 4/6: Direction",
                5: "Step 5/6: Stop loss", 
                6: "Step 6/6: Take profit"
            }
        }
        
        return messages.get(wizard_type, {}).get(current_step, f"Step {current_step}")
    
    # ===============================
    # Callback Handlers  
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for wizard operations. Returns True if handled."""
        if not data.startswith('wizard_'):
            return False
        
        try:
            if data == "wizard_cancel":
                await self._handle_callback_cancel(query)
            elif data == "wizard_help":
                await self._handle_callback_help(query)
            else:
                return False
            
            return True
            
        except Exception as e:
            await query.answer(f"Error: {str(e)}")
            return True
    
    async def _handle_callback_cancel(self, query):
        """Handle cancel wizard callback"""
        user_id = query.from_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            await self.edit_message(query, "ℹ️ No active wizard to cancel.")
            return
        
        session = self.session_manager.get_session(user_id)
        wizard_type = session.get('wizard_state', 'Unknown')
        
        self.session_manager.finish_wizard(user_id)
        
        await self.edit_message(
            query,
            f"❌ <b>Wizard Cancelled</b>\n\n"
            f"Operation: `{wizard_type}`\n"
            f"You can start a new operation anytime."
        )
    
    async def _handle_callback_help(self, query):
        """Handle wizard help callback"""
        help_text = (
            "🧙‍♂️ <b>Wizard Help</b>\n\n"
            "<b>Available Commands:</b>\n"
            "• `/cancel` - Cancel current wizard\n"
            "• `/wizardstatus` - Show wizard progress\n\n"
            "<b>Navigation:</b>\n"
            "• Follow step-by-step prompts\n"
            "• Enter requested information\n"
            "• Use buttons when available\n\n"
            "<b>Tips:</b>\n"
            "• Commands are case-insensitive\n"
            "• Type 'skip' to skip optional steps\n"
            "• Wizards timeout after 1 hour of inactivity"
        )
        
        await self.edit_message(query, help_text)
    
    # ===============================
    # Session Management
    # ===============================
    
    async def cleanup_expired_sessions(self):
        """Clean up expired wizard sessions"""
        self.session_manager.cleanup_old_sessions(max_age_hours=1)
        self.logger.info("Cleaned up expired wizard sessions")
    
    def get_active_wizards_count(self) -> int:
        """Get count of active wizards"""
        count = 0
        for session in self.session_manager.sessions.values():
            if session.get('wizard_state'):
                count += 1
        return count
    
    def get_wizard_statistics(self) -> Dict[str, Any]:
        """Get wizard usage statistics"""
        stats = {'total_sessions': len(self.session_manager.sessions)}
        
        wizard_types = {}
        for session in self.session_manager.sessions.values():
            wizard_type = session.get('wizard_state')
            if wizard_type:
                wizard_types[wizard_type] = wizard_types.get(wizard_type, 0) + 1
        
        stats['active_wizards'] = wizard_types
        return stats
    
    # ===============================
    # Abstract Method Implementation
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Main command dispatcher"""
        # This is handled by individual command handlers
        pass 