"""Bot Commands Handler - Handle bot listing, status, and log commands"""
import subprocess
import re
from typing import Dict, List, Optional, Any
from ..telegram_base import TelegramBaseHandler


class BotCommandsHandler(TelegramBaseHandler):
    """Handles bot listing, status, and log commands"""
    
    def __init__(self, session_manager):
        super().__init__('BotCommandsHandler')
        self.session_manager = session_manager
    
    # ===============================
    # Bot Listing & Status Commands
    # ===============================
    
    async def handle_list_command(self, update, context):
        """Handle /list command - List running containers"""
        try:
            containers = await self._get_running_containers()
            
            if not containers:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "📋 **Running Trading Bots**\n\n"
                    "❌ No trading bots are currently running.\n\n"
                    "💡 Use `/createbot` to create a new bot or `/startbot` to start an existing one."
                )
                return
            
            keyboard = self.create_keyboard([
                [("🔍 Details", "bot_details"), ("📊 Status", "bot_status_all")],
                [("🔄 Refresh", "bot_refresh"), ("❌ Close", "close")]
            ])
            
            message = "📋 **Running Trading Bots**\n\n"
            for i, container in enumerate(containers, 1):
                status_emoji = "🟢" if container['status'] == 'running' else "🟡"
                message += f"{status_emoji} **{i}. {container['name']}**\n"
                message += f"   Status: `{container['status']}`\n"
                message += f"   Created: `{container['created']}`\n"
                message += f"   Image: `{container['image']}`\n\n"
            
            message += f"**Total:** {len(containers)} containers"
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "list")
            )
    
    async def handle_status_command(self, update, context):
        """Handle /status command with container name"""
        try:
            if not context.args:
                await self._show_status_help(update, context)
                return
            
            container_name = context.args[0]
            status_info = await self._get_container_status(container_name)
            
            if not status_info:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ Container `{container_name}` not found.\n\n"
                    "Use `/list` to see available containers."
                )
                return
            
            keyboard = self.create_keyboard([
                [("📜 View Logs", f"bot_logs_{container_name}"), ("🔄 Restart", f"bot_restart_{container_name}")],
                [("⏹️ Stop", f"bot_stop_{container_name}"), ("🔄 Refresh", f"bot_status_{container_name}")],
                [("❌ Close", "close")]
            ])
            
            message = self._format_status_message(container_name, status_info)
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "status")
            )
    
    async def handle_logs_command(self, update, context):
        """Handle /logs command with container name"""
        try:
            if not context.args:
                await self._show_logs_help(update, context)
                return
            
            container_name = context.args[0]
            lines = int(context.args[1]) if len(context.args) > 1 else 20
            
            # Validate lines parameter
            if lines > 100:
                lines = 100
            elif lines < 1:
                lines = 20
            
            logs = await self._get_container_logs(container_name, lines)
            
            if not logs:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ No logs found for container `{container_name}`.\n\n"
                    "Container might not exist or have no logs yet."
                )
                return
            
            keyboard = self.create_keyboard([
                [("📜 More (50)", f"bot_logs_{container_name}_50"), ("📜 All (100)", f"bot_logs_{container_name}_100")],
                [("🔄 Refresh", f"bot_logs_{container_name}"), ("📊 Status", f"bot_status_{container_name}")],
                [("❌ Close", "close")]
            ])
            
            # Format logs message
            message = f"📜 **Logs: {container_name}**\n"
            message += f"Last {lines} lines:\n\n"
            message += f"```\n{logs}\n```"
            
            # Split message if too long
            if len(message) > 4000:
                # Send first part
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"📜 **Logs: {container_name}** (Part 1)\n\n```\n{logs[:3000]}\n```"
                )
                # Send second part with keyboard
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"📜 **Logs: {container_name}** (Part 2)\n\n```\n{logs[3000:]}\n```",
                    reply_markup=keyboard
                )
            else:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    message,
                    reply_markup=keyboard
                )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "logs")
            )
    
    # ===============================
    # Helper Commands
    # ===============================
    
    async def _show_status_help(self, update, context):
        """Show help for status command"""
        containers = await self._get_running_containers()
        
        message = "📊 **Bot Status Command**\n\n"
        message += "**Usage:** `/status <container_name>`\n\n"
        
        if containers:
            message += "**Available containers:**\n"
            for container in containers:
                message += f"• `{container['name']}`\n"
            message += "\n**Example:** `/status hyper_trader_1`"
        else:
            message += "❌ No containers are currently running.\n\n"
            message += "Use `/list` to see all containers or `/createbot` to create a new one."
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("🔄 Create Bot", "bot_create_start")],
            [("❌ Close", "close")]
        ])
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message,
            reply_markup=keyboard
        )
    
    async def _show_logs_help(self, update, context):
        """Show help for logs command"""
        containers = await self._get_running_containers()
        
        message = "📜 **Bot Logs Command**\n\n"
        message += "**Usage:** `/logs <container_name> [lines]`\n\n"
        message += "**Parameters:**\n"
        message += "• `container_name` - Name of the container\n"
        message += "• `lines` - Number of lines (1-100, default: 20)\n\n"
        
        if containers:
            message += "**Available containers:**\n"
            for container in containers:
                message += f"• `{container['name']}`\n"
            message += "\n**Examples:**\n"
            message += f"• `/logs {containers[0]['name']}` - Show last 20 lines\n"
            message += f"• `/logs {containers[0]['name']} 50` - Show last 50 lines"
        else:
            message += "❌ No containers are currently running."
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("❌ Close", "close")]
        ])
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message,
            reply_markup=keyboard
        )
    
    # ===============================
    # Docker Operations
    # ===============================
    
    async def _get_running_containers(self) -> List[Dict]:
        """Get list of running trading bot containers"""
        try:
            result = subprocess.run([
                'docker', 'ps', '--format',
                'table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}\t{{.Image}}'
            ], capture_output=True, text=True, check=True)
            
            containers = []
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        containers.append({
                            'name': parts[0].strip(),
                            'status': parts[1].strip(),
                            'created': parts[2].strip(),
                            'image': parts[3].strip()
                        })
            
            return containers
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting containers: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error getting containers: {e}")
            return []
    
    async def _get_container_status(self, container_name: str) -> Optional[Dict]:
        """Get detailed status of a specific container"""
        try:
            # Get container info
            result = subprocess.run([
                'docker', 'inspect', container_name
            ], capture_output=True, text=True, check=True)
            
            import json
            container_info = json.loads(result.stdout)[0]
            
            state = container_info['State']
            config = container_info['Config']
            
            return {
                'name': container_info['Name'].lstrip('/'),
                'status': state['Status'],
                'running': state['Running'],
                'started_at': state.get('StartedAt', 'Unknown'),
                'finished_at': state.get('FinishedAt', 'N/A'),
                'restart_count': state.get('RestartCount', 0),
                'image': config['Image'],
                'created': container_info['Created']
            }
            
        except subprocess.CalledProcessError:
            return None
        except Exception as e:
            self.logger.error(f"Error getting container status: {e}")
            return None
    
    async def _get_container_logs(self, container_name: str, lines: int = 20) -> Optional[str]:
        """Get logs from a specific container"""
        try:
            result = subprocess.run([
                'docker', 'logs', '--tail', str(lines), container_name
            ], capture_output=True, text=True, check=True)
            
            logs = result.stdout + result.stderr
            
            # Clean up logs
            if logs:
                # Remove excessive newlines
                logs = re.sub(r'\n{3,}', '\n\n', logs)
                # Truncate if too long
                if len(logs) > 3500:
                    logs = logs[-3500:] + "\n... (truncated)"
                
                return logs.strip()
            
            return "No logs available"
            
        except subprocess.CalledProcessError:
            return None
        except Exception as e:
            self.logger.error(f"Error getting container logs: {e}")
            return None
    
    def _format_status_message(self, container_name: str, status_info: Dict) -> str:
        """Format container status information"""
        status_emoji = {
            'running': '🟢',
            'exited': '🔴',
            'created': '🟡',
            'restarting': '🟠',
            'paused': '⏸️',
            'dead': '💀'
        }
        
        emoji = status_emoji.get(status_info['status'], '❓')
        
        message = f"{emoji} **Container Status: {container_name}**\n\n"
        message += f"**Status:** `{status_info['status']}`\n"
        message += f"**Running:** `{status_info['running']}`\n"
        message += f"**Image:** `{status_info['image']}`\n"
        message += f"**Created:** `{status_info['created'][:19]}`\n"
        
        if status_info['running']:
            message += f"**Started:** `{status_info['started_at'][:19]}`\n"
        else:
            message += f"**Finished:** `{status_info['finished_at'][:19]}`\n"
        
        message += f"**Restart Count:** `{status_info['restart_count']}`\n\n"
        
        if status_info['running']:
            message += "✅ Container is healthy and running"
        else:
            message += "❌ Container is not running"
        
        return message
    
    # ===============================
    # Command Router
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Handle command - Router for bot commands"""
        command = update.message.text.split()[0].lower()
        
        if command == '/logs':
            await self.handle_logs_command(update, context)
        elif command == '/status':
            await self.handle_status_command(update, context)
        else:
            # Fallback
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"❓ Command `{command}` not handled by BotCommandsHandler"
            )
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for bot commands"""
        try:
            if data == "bot_list":
                await query.edit_message_text("🔄 Refreshing container list...")
                # Simulate the /list command
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_list_command(fake_update, fake_context)
                return True
            
            elif data == "bot_refresh":
                await query.edit_message_text("🔄 Refreshing...")
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=query.message.chat.id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_list_command(fake_update, fake_context)
                return True
            
            elif data.startswith("bot_status_"):
                container_name = data.replace("bot_status_", "")
                status_info = await self._get_container_status(container_name)
                
                if status_info:
                    message = self._format_status_message(container_name, status_info)
                    keyboard = self.create_keyboard([
                        [("📜 Logs", f"bot_logs_{container_name}"), ("🔄 Restart", f"bot_restart_{container_name}")],
                        [("⏹️ Stop", f"bot_stop_{container_name}"), ("🔄 Refresh", f"bot_status_{container_name}")],
                        [("❌ Close", "close")]
                    ])
                    
                    await query.edit_message_text(message, reply_markup=keyboard)
                else:
                    await query.edit_message_text(f"❌ Container `{container_name}` not found.")
                
                return True
            
            elif data.startswith("bot_logs_"):
                parts = data.split("_")
                container_name = "_".join(parts[2:-1]) if len(parts) > 3 else parts[2]
                lines = int(parts[-1]) if parts[-1].isdigit() else 20
                
                logs = await self._get_container_logs(container_name, lines)
                
                if logs:
                    message = f"📜 **Logs: {container_name}**\n"
                    message += f"Last {lines} lines:\n\n```\n{logs}\n```"
                    
                    keyboard = self.create_keyboard([
                        [("📜 More (50)", f"bot_logs_{container_name}_50"), ("📜 All (100)", f"bot_logs_{container_name}_100")],
                        [("🔄 Refresh", f"bot_logs_{container_name}"), ("📊 Status", f"bot_status_{container_name}")],
                        [("❌ Close", "close")]
                    ])
                    
                    if len(message) > 4000:
                        await query.edit_message_text(f"📜 **Logs too long, showing last 2000 chars:**\n\n```\n{logs[-2000:]}\n```", reply_markup=keyboard)
                    else:
                        await query.edit_message_text(message, reply_markup=keyboard)
                else:
                    await query.edit_message_text(f"❌ No logs found for `{container_name}`")
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in callback handler: {e}")
            await query.edit_message_text(f"❌ Error: {str(e)}")
            return True 