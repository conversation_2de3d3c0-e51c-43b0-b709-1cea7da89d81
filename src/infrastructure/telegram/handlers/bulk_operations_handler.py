"""Bulk Operations Handler - Manage multiple trading bots simultaneously"""
import asyncio
import subprocess
import json
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from ..telegram_base import TelegramBaseHandler


class BulkOperationsHandler(TelegramBaseHandler):
    """Handle bulk operations for multiple trading bots"""
    
    def __init__(self, session_manager):
        super().__init__('BulkOperationsHandler')
        self.session_manager = session_manager
        
        # Track ongoing bulk operations
        self.active_operations: Dict[int, Dict] = {}  # chat_id: operation_data
    
    # ===============================
    # Bulk Creation Commands
    # ===============================
    
    async def handle_bulk_create_command(self, update, context):
        """Handle /bulkcreate command - Create multiple bots at once"""
        try:
            chat_id = update.effective_chat.id
            
            # Check if bulk operation already in progress
            if chat_id in self.active_operations:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "⚠️ <b>Bulk operation in progress</b>\n\n"
                    "Please wait for the current operation to complete.\n\n"
                    "Use `/bulkstatus` to check progress."
                )
                return
            
            # Show bulk creation options
            keyboard = self.create_keyboard([
                [("📊 Quick Setup (3 Bots)", "bulk_quick_3"), ("🚀 Standard Setup (5 Bots)", "bulk_quick_5")],
                [("⚙️ Custom Setup", "bulk_custom"), ("📋 From Template", "bulk_template")],
                [("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                "📦 <b>Bulk Bot Creation</b>\n\n"
                "Create multiple trading bots simultaneously.\n\n"
                "<b>Options:</b>\n"
                "📊 <b>Quick Setup</b> - Pre-configured popular symbols\n"
                "🚀 <b>Standard Setup</b> - Balanced portfolio approach\n"
                "⚙️ <b>Custom Setup</b> - Choose your own symbols\n"
                "📋 <b>Template</b> - Use saved configuration templates\n\n"
                "💡 All bots will use the same credentials and base config.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "bulk_create")
            )
    
    async def handle_bulk_deploy_command(self, update, context):
        """Handle /bulkdeploy command - Deploy multiple strategies"""
        try:
            chat_id = update.effective_chat.id
            
            # Get available strategies
            strategies = await self._get_strategy_templates()
            
            if not strategies:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "❌ <b>No Strategy Templates Found</b>\n\n"
                    "You need to create strategy templates first.\n\n"
                    "Use `/createconfig` to create trading configurations."
                )
                return
            
            message = "🚀 <b>Bulk Strategy Deployment</b>\n\n"
            message += "Deploy multiple pre-configured strategies:\n\n"
            
            for i, strategy in enumerate(strategies[:5], 1):
                strategy_name = strategy.get('name', 'Unknown')
                symbols = strategy.get('symbols', ['N/A'])
                message += f"{i}. <b>{strategy_name}</b>\n"
                message += f"   Symbols: {', '.join(symbols[:3])}\n"
                message += f"   Risk: {strategy.get('risk_level', 'Medium')}\n\n"
            
            keyboard_buttons = []
            for strategy in strategies[:5]:
                strategy_name = strategy.get('name', 'Unknown')[:15]
                keyboard_buttons.append([(f"🚀 Deploy {strategy_name}", f"bulk_deploy_{strategy['id']}")])
            
            keyboard_buttons.append([("🔄 Deploy All", "bulk_deploy_all"), ("❌ Cancel", "close")])
            keyboard = self.create_keyboard(keyboard_buttons)
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "bulk_deploy")
            )
    
    # ===============================
    # Bulk Management Commands
    # ===============================
    
    async def handle_bulk_status_command(self, update, context):
        """Handle /bulkstatus command - Show status of all bots"""
        try:
            chat_id = update.effective_chat.id
            
            # Check for active bulk operation
            if chat_id in self.active_operations:
                operation = self.active_operations[chat_id]
                await self._show_operation_progress(update, context, operation)
                return
            
            # Get all containers status
            containers = await self._get_all_containers_status()
            
            if not containers:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "📊 <b>Bulk Status Overview</b>\n\n"
                    "❌ No trading bots found.\n\n"
                    "Use `/bulkcreate` to create multiple bots."
                )
                return
            
            # Categorize containers by status
            running = [c for c in containers if c['status'] == 'running']
            stopped = [c for c in containers if c['status'] in ['exited', 'stopped']]
            other = [c for c in containers if c['status'] not in ['running', 'exited', 'stopped']]
            
            message = "📊 <b>Bulk Status Overview</b>\n\n"
            message += f"<b>Total Containers:</b> {len(containers)}\n"
            message += f"🟢 <b>Running:</b> {len(running)}\n"
            message += f"🔴 <b>Stopped:</b> {len(stopped)}\n"
            message += f"🟡 <b>Other:</b> {len(other)}\n\n"
            
            # Show top 5 running containers
            if running:
                message += "<b>🟢 Running Containers:</b>\n"
                for container in running[:5]:
                    uptime = self._calculate_uptime(container.get('started_at', ''))
                    message += f"• `{container['name']}` - {uptime}\n"
                
                if len(running) > 5:
                    message += f"... and {len(running) - 5} more\n"
            
            message += "\n"
            
            # Show stopped containers
            if stopped:
                message += "<b>🔴 Stopped Containers:</b>\n"
                for container in stopped[:3]:
                    message += f"• `{container['name']}`\n"
                
                if len(stopped) > 3:
                    message += f"... and {len(stopped) - 3} more\n"
            
            keyboard = self.create_keyboard([
                [("🚀 Start All", "bulk_start_all"), ("🛑 Stop All", "bulk_stop_all")],
                [("🔄 Restart All", "bulk_restart_all"), ("📊 Detailed View", "bulk_detailed")],
                [("🔄 Refresh", "bulk_status"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "bulk_status")
            )
    
    async def handle_bulk_logs_command(self, update, context):
        """Handle /bulklogs command - Aggregate logs from multiple bots"""
        try:
            chat_id = update.effective_chat.id
            
            # Get running containers
            containers = await self._get_running_containers()
            
            if not containers:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "📜 <b>Bulk Logs</b>\n\n"
                    "❌ No running containers found.\n\n"
                    "Start some bots first to view their logs."
                )
                return
            
            # Limit to 5 containers for readability
            if len(containers) > 5:
                containers = containers[:5]
                truncated_msg = f"\n⚠️ Showing logs from first 5 of {len(containers)} containers"
            else:
                truncated_msg = ""
            
            # Collect logs from all containers
            all_logs = []
            for container in containers:
                logs = await self._get_container_logs(container['name'], 10)
                if logs:
                    all_logs.append(f"📋 <b>{container['name']}:</b>\n```\n{logs[-500:]}\n```\n")
            
            if not all_logs:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "📜 <b>Bulk Logs</b>\n\n"
                    "❌ No logs available from running containers."
                )
                return
            
            # Combine logs with size limit
            combined_logs = f"📜 <b>Bulk Logs Summary</b>{truncated_msg}\n\n"
            combined_logs += "\n".join(all_logs)
            
            # Split if too long
            if len(combined_logs) > 4000:
                # Send in parts
                parts = self._split_message(combined_logs, 3800)
                for i, part in enumerate(parts):
                    if i == len(parts) - 1:  # Last part gets keyboard
                        keyboard = self.create_keyboard([
                            [("🔄 Refresh", "bulk_logs"), ("📊 Status", "bulk_status")],
                            [("❌ Close", "close")]
                        ])
                        await self.send_message(context.bot, chat_id, part, reply_markup=keyboard)
                    else:
                        await self.send_message(context.bot, chat_id, part)
            else:
                keyboard = self.create_keyboard([
                    [("🔄 Refresh", "bulk_logs"), ("📊 Status", "bulk_status")],
                    [("❌ Close", "close")]
                ])
                await self.send_message(context.bot, chat_id, combined_logs, reply_markup=keyboard)
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "bulk_logs")
            )
    
    # ===============================
    # Bulk Operations Execution
    # ===============================
    
    async def _execute_bulk_creation(self, chat_id: int, bot, creation_plan: Dict):
        """Execute bulk bot creation with progress tracking"""
        try:
            operation_id = f"bulk_create_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # Initialize operation tracking
            operation = {
                'id': operation_id,
                'type': 'bulk_create',
                'chat_id': chat_id,
                'total_bots': len(creation_plan['bots']),
                'completed_bots': 0,
                'failed_bots': 0,
                'started_at': datetime.now(),
                'status': 'in_progress',
                'results': []
            }
            
            self.active_operations[chat_id] = operation
            
            # Send initial progress message
            progress_msg = await bot.send_message(
                chat_id=chat_id,
                text=f"🚀 <b>Starting Bulk Creation</b>\n\n"
                f"Creating {operation['total_bots']} trading bots...\n"
                f"Progress: 0/{operation['total_bots']} (0%)",
                parse_mode='Markdown'
            )
            
            # Create bots one by one
            for i, bot_config in enumerate(creation_plan['bots'], 1):
                try:
                    # Update progress
                    progress_text = (
                        f"🚀 <b>Bulk Creation Progress</b>\n\n"
                        f"Creating: `{bot_config['name']}`\n"
                        f"Progress: {i-1}/{operation['total_bots']} ({((i-1)/operation['total_bots']*100):.0f}%)\n\n"
                        f"{'▓' * int((i-1)/operation['total_bots'] * 20)}{'░' * (20 - int((i-1)/operation['total_bots'] * 20))}"
                    )
                    
                    await progress_msg.edit_text(progress_text, parse_mode='Markdown')
                    
                    # Create the bot
                    success = await self._create_single_bot(bot_config)
                    
                    # Record result
                    result = {
                        'name': bot_config['name'],
                        'success': success,
                        'timestamp': datetime.now().isoformat()
                    }
                    operation['results'].append(result)
                    
                    if success:
                        operation['completed_bots'] += 1
                    else:
                        operation['failed_bots'] += 1
                    
                    # Small delay between creations
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    self.logger.error(f"Error creating bot {bot_config['name']}: {e}")
                    operation['failed_bots'] += 1
                    operation['results'].append({
                        'name': bot_config['name'],
                        'success': False,
                        'error': str(e),
                        'timestamp': datetime.now().isoformat()
                    })
            
            # Operation complete
            operation['status'] = 'completed'
            operation['completed_at'] = datetime.now()
            
            # Send final results
            await self._send_bulk_results(bot, chat_id, operation)
            
            # Cleanup
            del self.active_operations[chat_id]
            
        except Exception as e:
            self.logger.error(f"Error in bulk creation: {e}")
            operation['status'] = 'failed'
            operation['error'] = str(e)
            
            await bot.send_message(
                chat_id=chat_id,
                text=f"❌ <b>Bulk Creation Failed</b>\n\n`{str(e)}`",
                parse_mode='Markdown'
            )
            
            # Cleanup
            if chat_id in self.active_operations:
                del self.active_operations[chat_id]
    
    async def _execute_bulk_operation(self, chat_id: int, bot, operation_type: str, containers: List[str]):
        """Execute bulk start/stop/restart operations"""
        try:
            operation = {
                'type': operation_type,
                'total': len(containers),
                'completed': 0,
                'failed': 0,
                'started_at': datetime.now(),
                'status': 'in_progress',
                'results': []
            }
            
            self.active_operations[chat_id] = operation
            
            # Send progress message
            action_name = operation_type.replace('_', ' ').title()
            progress_msg = await bot.send_message(
                chat_id=chat_id,
                text=f"⚙️ <b>{action_name} Operation</b>\n\n"
                f"Processing {len(containers)} containers...\n"
                f"Progress: 0/{len(containers)} (0%)",
                parse_mode='Markdown'
            )
            
            # Process containers
            for i, container_name in enumerate(containers, 1):
                try:
                    # Update progress
                    progress_text = (
                        f"⚙️ <b>{action_name} Progress</b>\n\n"
                        f"Processing: `{container_name}`\n"
                        f"Progress: {i-1}/{len(containers)} ({((i-1)/len(containers)*100):.0f}%)\n\n"
                        f"{'▓' * int((i-1)/len(containers) * 20)}{'░' * (20 - int((i-1)/len(containers) * 20))}"
                    )
                    
                    await progress_msg.edit_text(progress_text, parse_mode='Markdown')
                    
                    # Execute operation
                    if operation_type == 'bulk_start':
                        success = await self._start_container(container_name)
                    elif operation_type == 'bulk_stop':
                        success = await self._stop_container(container_name)
                    elif operation_type == 'bulk_restart':
                        success = await self._restart_container(container_name)
                    else:
                        success = False
                    
                    # Record result
                    operation['results'].append({
                        'container': container_name,
                        'success': success,
                        'timestamp': datetime.now().isoformat()
                    })
                    
                    if success:
                        operation['completed'] += 1
                    else:
                        operation['failed'] += 1
                    
                    # Small delay
                    await asyncio.sleep(1)
                    
                except Exception as e:
                    self.logger.error(f"Error in {operation_type} for {container_name}: {e}")
                    operation['failed'] += 1
            
            # Send final results
            operation['status'] = 'completed'
            
            final_text = (
                f"✅ <b>{action_name} Complete</b>\n\n"
                f"<b>Results:</b>\n"
                f"✅ Successful: {operation['completed']}\n"
                f"❌ Failed: {operation['failed']}\n"
                f"📊 Total: {operation['total']}\n\n"
                f"Duration: {(datetime.now() - operation['started_at']).seconds}s"
            )
            
            keyboard = self.create_keyboard([
                [("📊 Status", "bulk_status"), ("📜 Logs", "bulk_logs")],
                [("❌ Close", "close")]
            ])
            
            await progress_msg.edit_text(final_text, reply_markup=keyboard, parse_mode='Markdown')
            
            # Cleanup
            del self.active_operations[chat_id]
            
        except Exception as e:
            self.logger.error(f"Error in bulk operation {operation_type}: {e}")
            await bot.send_message(
                chat_id=chat_id,
                text=f"❌ <b>{operation_type.title()} Failed</b>\n\n`{str(e)}`",
                parse_mode='Markdown'
            )
            
            if chat_id in self.active_operations:
                del self.active_operations[chat_id]
    
    # ===============================
    # Helper Methods
    # ===============================
    
    async def _show_operation_progress(self, update, context, operation: Dict):
        """Show progress of ongoing bulk operation"""
        try:
            chat_id = update.effective_chat.id
            
            operation_type = operation.get('type', 'Unknown')
            total = operation.get('total_bots', operation.get('total', 0))
            completed = operation.get('completed_bots', operation.get('completed', 0))
            failed = operation.get('failed_bots', operation.get('failed', 0))
            
            progress_percent = (completed + failed) / total * 100 if total > 0 else 0
            
            message = f"⚙️ <b>{operation_type.replace('_', ' ').title()} Progress</b>\n\n"
            message += f"Status: `{operation.get('status', 'Unknown')}`\n"
            message += f"Progress: {completed + failed}/{total} ({progress_percent:.0f}%)\n"
            message += f"✅ Completed: {completed}\n"
            message += f"❌ Failed: {failed}\n\n"
            message += f"{'▓' * int(progress_percent / 5)}{'░' * (20 - int(progress_percent / 5))}"
            
            keyboard = self.create_keyboard([
                [("🔄 Refresh", "bulk_status"), ("❌ Cancel", "bulk_cancel")],
                [("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "show_operation_progress")
            )
    
    async def _get_all_containers_status(self) -> List[Dict]:
        """Get status of all containers"""
        try:
            result = subprocess.run([
                'docker', 'ps', '-a', '--format',
                'table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}\t{{.RunningFor}}'
            ], capture_output=True, text=True, check=True)
            
            containers = []
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 4:
                        containers.append({
                            'name': parts[0].strip(),
                            'status': parts[1].strip().split()[0],  # Get just status word
                            'created_at': parts[2].strip(),
                            'running_for': parts[3].strip()
                        })
            
            return containers
            
        except Exception as e:
            self.logger.error(f"Error getting containers status: {e}")
            return []
    
    async def _get_running_containers(self) -> List[Dict]:
        """Get list of running containers"""
        containers = await self._get_all_containers_status()
        return [c for c in containers if c['status'] == 'running']
    
    async def _get_strategy_templates(self) -> List[Dict]:
        """Get available strategy templates"""
        # This would load from saved strategy templates
        # For now, return some example templates
        return [
            {
                'id': 'scalping_multi',
                'name': 'Multi-Symbol Scalping',
                'symbols': ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT'],
                'risk_level': 'High'
            },
            {
                'id': 'dca_stable',
                'name': 'DCA Stable Coins',
                'symbols': ['BTC/USDT:USDT', 'ETH/USDT:USDT'],
                'risk_level': 'Low'
            },
            {
                'id': 'momentum_swing',
                'name': 'Momentum Swing Trading',
                'symbols': ['HYPER/USDT:USDT', 'SOL/USDT:USDT', 'ADA/USDT:USDT'],
                'risk_level': 'Medium'
            }
        ]
    
    async def _create_single_bot(self, bot_config: Dict) -> bool:
        """Create a single bot from configuration"""
        try:
            # This would execute the actual bot creation
            # For now, simulate with a delay
            await asyncio.sleep(1)
            
            # In real implementation, this would:
            # 1. Validate bot_config
            # 2. Execute bot.sh with proper parameters
            # 3. Monitor container creation
            # 4. Return success/failure
            
            self.logger.info(f"Created bot: {bot_config['name']}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating bot {bot_config.get('name', 'unknown')}: {e}")
            return False
    
    async def _start_container(self, container_name: str) -> bool:
        """Start a container"""
        try:
            result = subprocess.run([
                'docker', 'start', container_name
            ], capture_output=True, text=True, check=True)
            return True
        except Exception:
            return False
    
    async def _stop_container(self, container_name: str) -> bool:
        """Stop a container"""
        try:
            result = subprocess.run([
                'docker', 'stop', container_name
            ], capture_output=True, text=True, check=True, timeout=30)
            return True
        except Exception:
            return False
    
    async def _restart_container(self, container_name: str) -> bool:
        """Restart a container"""
        try:
            result = subprocess.run([
                'docker', 'restart', container_name
            ], capture_output=True, text=True, check=True, timeout=60)
            return True
        except Exception:
            return False
    
    async def _get_container_logs(self, container_name: str, lines: int = 10) -> Optional[str]:
        """Get container logs"""
        try:
            result = subprocess.run([
                'docker', 'logs', '--tail', str(lines), container_name
            ], capture_output=True, text=True, check=True)
            
            return result.stdout + result.stderr
            
        except Exception:
            return None
    
    def _calculate_uptime(self, started_at: str) -> str:
        """Calculate container uptime"""
        try:
            # Parse the started_at time and calculate uptime
            # This is a simplified version
            return "Running"
        except Exception:
            return "Unknown"
    
    def _split_message(self, message: str, max_length: int) -> List[str]:
        """Split long message into parts"""
        parts = []
        while len(message) > max_length:
            # Find a good break point (prefer newlines)
            break_point = message.rfind('\n', 0, max_length)
            if break_point == -1:
                break_point = max_length
            
            parts.append(message[:break_point])
            message = message[break_point:].lstrip()
        
        if message:
            parts.append(message)
        
        return parts
    
    async def _send_bulk_results(self, bot, chat_id: int, operation: Dict):
        """Send bulk operation results"""
        try:
            results = operation['results']
            successful = [r for r in results if r['success']]
            failed = [r for r in results if not r['success']]
            
            duration = (operation.get('completed_at', datetime.now()) - operation['started_at']).seconds
            
            message = "🎉 <b>Bulk Creation Complete</b>\n\n"
            message += f"<b>Summary:</b>\n"
            message += f"✅ Successful: {len(successful)}\n"
            message += f"❌ Failed: {len(failed)}\n"
            message += f"📊 Total: {len(results)}\n"
            message += f"⏱️ Duration: {duration}s\n\n"
            
            if successful:
                message += "<b>✅ Successfully Created:</b>\n"
                for result in successful:
                    message += f"• `{result['name']}`\n"
                message += "\n"
            
            if failed:
                message += "<b>❌ Failed to Create:</b>\n"
                for result in failed[:5]:  # Limit to 5
                    message += f"• `{result['name']}`\n"
                
                if len(failed) > 5:
                    message += f"... and {len(failed) - 5} more\n"
            
            keyboard = self.create_keyboard([
                [("📊 Status", "bulk_status"), ("📜 Logs", "bulk_logs")],
                [("🚀 Create More", "bulk_create"), ("❌ Close", "close")]
            ])
            
            await bot.send_message(
                chat_id=chat_id,
                text=message,
                reply_markup=keyboard,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            self.logger.error(f"Error sending bulk results: {e}")
    
    # ===============================
    # Command Router
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Handle command - Router for bulk operations commands"""
        command = update.message.text.split()[0].lower()
        
        if command == '/bulkcreate':
            await self.handle_bulk_create_command(update, context)
        elif command == '/bulkdeploy':
            await self.handle_bulk_deploy_command(update, context)
        elif command == '/bulkstatus':
            await self.handle_bulk_status_command(update, context)
        elif command == '/bulklogs':
            await self.handle_bulk_logs_command(update, context)
        else:
            # Fallback
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"❓ Command `{command}` not handled by BulkOperationsHandler"
            )
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for bulk operations"""
        try:
            chat_id = query.message.chat_id
            
            if data.startswith("bulk_quick_"):
                bot_count = int(data.split("_")[-1])
                
                # Generate quick setup plan
                quick_symbols = ['BTC/USDT:USDT', 'ETH/USDT:USDT', 'BNB/USDT:USDT', 'HYPER/USDT:USDT', 'SOL/USDT:USDT']
                
                creation_plan = {
                    'bots': [
                        {
                            'name': f"{symbol.split('/')[0].lower()}_trader_{i}",
                            'symbol': symbol,
                            'amount': 100,
                            'strategy': 'dca'
                        }
                        for i, symbol in enumerate(quick_symbols[:bot_count], 1)
                    ]
                }
                
                await query.edit_message_text(
                    f"🚀 Starting quick creation of {bot_count} bots...\n\n"
                    f"This will create trading bots for: {', '.join([s.split('/')[0] for s in quick_symbols[:bot_count]])}"
                )
                
                # Start bulk creation in background
                asyncio.create_task(self._execute_bulk_creation(chat_id, query.bot, creation_plan))
                return True
            
            elif data == "bulk_start_all":
                containers = await self._get_all_containers_status()
                stopped_containers = [c['name'] for c in containers if c['status'] != 'running']
                
                if not stopped_containers:
                    await query.edit_message_text("ℹ️ All containers are already running.")
                    return True
                
                await query.edit_message_text(f"🚀 Starting {len(stopped_containers)} containers...")
                asyncio.create_task(self._execute_bulk_operation(chat_id, query.bot, 'bulk_start', stopped_containers))
                return True
            
            elif data == "bulk_stop_all":
                containers = await self._get_running_containers()
                if not containers:
                    await query.edit_message_text("ℹ️ No running containers to stop.")
                    return True
                
                container_names = [c['name'] for c in containers]
                await query.edit_message_text(f"🛑 Stopping {len(container_names)} containers...")
                asyncio.create_task(self._execute_bulk_operation(chat_id, query.bot, 'bulk_stop', container_names))
                return True
            
            elif data == "bulk_restart_all":
                containers = await self._get_running_containers()
                if not containers:
                    await query.edit_message_text("ℹ️ No running containers to restart.")
                    return True
                
                container_names = [c['name'] for c in containers]
                await query.edit_message_text(f"🔄 Restarting {len(container_names)} containers...")
                asyncio.create_task(self._execute_bulk_operation(chat_id, query.bot, 'bulk_restart', container_names))
                return True
            
            elif data == "bulk_status":
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_bulk_status_command(fake_update, fake_context)
                return True
            
            elif data == "bulk_logs":
                from types import SimpleNamespace
                fake_update = SimpleNamespace(effective_chat=SimpleNamespace(id=chat_id))
                fake_context = SimpleNamespace(bot=query.bot)
                await self.handle_bulk_logs_command(fake_update, fake_context)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in bulk callback handler: {e}")
            await query.edit_message_text(f"❌ Error: {str(e)}")
            return True 