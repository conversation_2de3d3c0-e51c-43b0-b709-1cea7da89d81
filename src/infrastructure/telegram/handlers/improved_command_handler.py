#!/usr/bin/env python3
"""Improved Telegram Command Handler with comprehensive functionality."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from telegram.constants import ParseMode

from ..telegram_base import TelegramBaseHandler, UserSessionManager, ValidationUtils
from ..templates import TelegramTemplates
from ..trading_notifications import TradingNotificationManager


class ImprovedTelegramCommandHandler(TelegramBaseHandler):
    """Enhanced Telegram command handler with comprehensive bot management features."""
    
    def __init__(self, bot_token: str, chat_id: int):
        super().__init__('ImprovedTelegramCommandHandler')
        self.bot_token = bot_token
        self.chat_id = chat_id
        self.application = None
        self.session_manager = UserSessionManager()
        self.bot_script_path = "./bot.sh"  # Path to bot.sh script

        # Initialize notification manager
        self.notification_manager = TradingNotificationManager(bot_token, chat_id)
    
    def start(self):
        """Start the Telegram bot with polling (non-async version)"""
        try:
            print("🔧 Creating Telegram application...")
            # Create application
            self.application = Application.builder().token(self.bot_token).build()

            print("📝 Adding command handlers...")
            # Add command handlers
            self.application.add_handler(CommandHandler("start", self.handle_start))
            self.application.add_handler(CommandHandler("help", self.handle_help))
            self.application.add_handler(CommandHandler("cancel", self.handle_cancel))

            # Credential commands
            self.application.add_handler(CommandHandler("addcreds", self.handle_addcreds_wizard))
            self.application.add_handler(CommandHandler("listcreds", self.handle_listcreds))
            self.application.add_handler(CommandHandler("loadcreds", self.handle_loadcreds))
            self.application.add_handler(CommandHandler("showcreds", self.handle_showcreds))
            self.application.add_handler(CommandHandler("setkey", self.handle_setkey))

            # Bot management commands
            self.application.add_handler(CommandHandler("createbot", self.handle_createbot_wizard))
            self.application.add_handler(CommandHandler("startbot", self.handle_startbot))
            self.application.add_handler(CommandHandler("list", self.handle_list))
            self.application.add_handler(CommandHandler("status", self.handle_status))
            self.application.add_handler(CommandHandler("logs", self.handle_logs))
            self.application.add_handler(CommandHandler("stop", self.handle_stopbot))
            self.application.add_handler(CommandHandler("restart", self.handle_restart))
            self.application.add_handler(CommandHandler("stopall", self.handle_stopall))

            # Config management commands
            self.application.add_handler(CommandHandler("createconfig", self.handle_createconfig))
            self.application.add_handler(CommandHandler("listconfigs", self.handle_listconfigs))
            self.application.add_handler(CommandHandler("showconfig", self.handle_showconfig))

            # Notification commands
            self.application.add_handler(CommandHandler("subscribe", self.handle_subscribe))
            self.application.add_handler(CommandHandler("unsubscribe", self.handle_unsubscribe))
            self.application.add_handler(CommandHandler("alerts", self.handle_alerts))
            self.application.add_handler(CommandHandler("testnotify", self.handle_test_notification))

            # Callback query handler for wizards
            self.application.add_handler(CallbackQueryHandler(self.handle_callback_query))

            # Message handler for wizard inputs
            self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_wizard_message))

            # Add error handler
            self.application.add_error_handler(self.handle_error)

            # Run the bot - this blocks until stopped
            print("🚀 Starting bot polling...")
            self.logger.info("Starting Telegram bot polling...")
            self.application.run_polling(allowed_updates=Update.ALL_TYPES)

        except Exception as e:
            print(f"❌ Error starting Telegram bot: {e}")
            self.logger.error(f"Error starting Telegram bot: {e}")
            raise
    
    async def handle_error(self, update: object, context) -> None:
        """Handle errors that occur during bot operation"""
        self.logger.error(f"Telegram bot error: {context.error}")
        
        # Try to send error message to user if possible
        try:
            if update and hasattr(update, 'effective_chat') and update.effective_chat:
                await context.bot.send_message(
                    chat_id=update.effective_chat.id,
                    text="❌ <b>Xảy ra lỗi khi xử lý lệnh</b>\n\nVui lòng thử lại sau.",
                    parse_mode=ParseMode.HTML
                )
        except Exception as e:
            self.logger.error(f"Failed to send error message: {e}")
    
    def escape_html(self, text: str) -> str:
        """Escape HTML special characters in text"""
        if not text:
            return ""
        return (text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace('"', "&quot;")
                   .replace("'", "&#x27;"))
    
    async def stop(self):
        """Stop the Telegram bot"""
        if hasattr(self, 'application') and self.application:
            try:
                await self.application.stop()
                await self.application.shutdown()
            except Exception as e:
                self.logger.error(f"Error stopping application: {e}")
    
    async def handle_command(self, update, context) -> None:
        """Handle command - required by base class"""
        # This is handled by individual command handlers
        pass

    # Basic Commands
    async def handle_start(self, update: Update, context) -> None:
        """Handle /start command"""
        self.logger.info(f"Received /start command from user {update.effective_user.id} in chat {update.effective_chat.id}")

        user = update.effective_user
        welcome_text = f"""
🤖 <b>AutoTrader Bot</b> chào mừng {user.mention_html()}!

Tôi là bot quản lý giao dịch tự động của bạn.

<b>Lệnh cơ bản:</b>
• `/help` - Xem tất cả lệnh
• `/addcreds` - Thêm thông tin tài khoản
• `/createbot` - Tạo bot trading

Hãy bắt đầu bằng cách thêm thông tin tài khoản với `/addcreds`!
        """

        keyboard = [
            [InlineKeyboardButton("📖 Hướng dẫn", callback_data="help_guide")],
            [InlineKeyboardButton("⚙️ Thêm tài khoản", callback_data="start_addcreds")],
            [InlineKeyboardButton("🤖 Tạo bot", callback_data="start_createbot")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            welcome_text,
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )

    async def handle_help(self, update: Update, context) -> None:
        """Handle /help command"""
        help_text = """
🤖 <b>AutoTrader Bot - Hướng dẫn sử dụng</b>

<b>📋 Quản lý tài khoản:</b>
• `/addcreds` - Thêm thông tin tài khoản exchange
• `/listcreds` - Xem danh sách tài khoản
• <code>/loadcreds &lt;profile&gt;</code> - Tải profile tài khoản
• <code>/setkey &lt;key&gt; &lt;secret&gt;</code> - Lưu key/secret nhanh

<b>🤖 Quản lý bot:</b>
• `/createbot` - Tạo bot mới (wizard)
• `/startbot <symbol> <amount>` - Khởi động nhanh
• `/list` - Xem tất cả bot
• `/status <symbol>` - Trạng thái bot
• `/logs <symbol> [lines]` - Xem log
• `/stop <symbol>` - Dừng bot
• `/restart <symbol>` - Khởi động lại
• `/stopall` - Dừng tất cả bot

<b>⚙️ Config & Notifications:</b>
• `/listconfigs` - Xem danh sách config
• `/showconfig <name>` - Xem nội dung config
• `/subscribe` - Đăng ký thông báo
• `/unsubscribe` - Hủy thông báo
• `/alerts` - Cấu hình cảnh báo
• `/testnotify [type]` - Test thông báo

<b>Ví dụ:</b>
• `/setkey abc123 xyz789` - Lưu API key
• `/startbot BTCUSDT 100` - Trade BTC với $100
• `/logs BTCUSDT 20` - Xem 20 dòng log mới nhất
• `/cancel` - Hủy wizard hiện tại

💡 Sử dụng `/createbot` để setup chi tiết hơn!
        """
        
        # Check if this is from callback query or regular message
        if update.callback_query:
            # From callback query (button click)
            query = update.callback_query
            await query.edit_message_text(help_text, parse_mode=ParseMode.HTML)
        else:
            # From regular message command
            await update.message.reply_text(help_text, parse_mode=ParseMode.HTML)

    async def handle_cancel(self, update: Update, context) -> None:
        """Handle /cancel command - cancel current wizard"""
        user_id = update.effective_user.id

        session = self.session_manager.get_session(user_id)
        if not session or not session.get('wizard_state'):
            await update.message.reply_text(
                "❌ <b>Không có thao tác nào để hủy</b>\n\n"
                "Bạn không đang trong quá trình thực hiện wizard nào.",
                parse_mode=ParseMode.HTML
            )
            return

        wizard_type = session['wizard_state']
        self.session_manager.clear_session(user_id)

        await update.message.reply_text(
            f"✅ <b>Đã hủy {wizard_type}</b>\n\n"
            "Bạn có thể bắt đầu lại bất cứ lúc nào.",
            parse_mode=ParseMode.HTML
        )

    # Credential Management Commands
    async def handle_setkey(self, update: Update, context) -> None:
        """Handle /setkey command"""
        chat_id = update.effective_chat.id
        
        # Get arguments
        args = context.args if context.args else []
        
        if len(args) != 2:
            help_text = """
🔐 <b>Lưu API Key nhanh</b>

Cú pháp: <code>/setkey &lt;api_key&gt; &lt;api_secret&gt;</code>

Ví dụ:
• <code>/setkey abc123... xyz789...</code>

💡 Lệnh này lưu key vào profile "default"
Sử dụng <code>/addcreds</code> để tạo nhiều profile khác nhau.
            """
            await update.message.reply_text(help_text, parse_mode=ParseMode.HTML)
            return
        
        api_key, api_secret = args[0], args[1]
        
        # Validate inputs
        if not ValidationUtils.validate_api_key(api_key):
            await update.message.reply_text(
                "❌ <b>API key không hợp lệ</b>\n\nKey phải có ít nhất 10 ký tự.",
                parse_mode=ParseMode.HTML
            )
            return
        
        # Store credentials using bot.sh
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "store-credentials", "default", api_key, api_secret, "Default Account"
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    "✅ <b>Lưu thành công!</b>\n\n"
                    "API key đã được lưu vào profile 'default'.\n"
                    "Bây giờ bạn có thể tạo bot với `/createbot`",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi lưu key:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_listcreds(self, update: Update, context) -> None:
        """Handle /listcreds command"""
        self.logger.info(f"Received /listcreds command from user {update.effective_user.id} in chat {update.effective_chat.id}")

        chat_id = update.effective_chat.id

        try:
            # Get credentials list using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list-credentials"])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📋 <b>Danh sách tài khoản:</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_loadcreds(self, update: Update, context) -> None:
        """Handle /loadcreds command"""
        chat_id = update.effective_chat.id
        
        # Get profile argument
        profile = context.args[0] if context.args else "default"
        
        try:
            # Load credentials using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "load-credentials", profile])
            
            if result[0] == 0:
                success_msg = f"✅ <b>Tải thành công!</b>\n\n"
                success_msg += f"Profile: <code>{self.escape_html(profile)}</code>\n\n"
                success_msg += "<b>Sẵn sàng tạo bot:</b>\n"
                success_msg += "• <code>/createbot</code> - Tạo bot với wizard\n"
                success_msg += "• <code>/startbot symbol amount</code> - Khởi động nhanh\n"
                success_msg += "• <code>/list</code> - Xem tất cả bot"
                
                await update.message.reply_text(success_msg, parse_mode=ParseMode.HTML)
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi tải profile '{self.escape_html(profile)}':</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_showcreds(self, update: Update, context) -> None:
        """Handle /showcreds command"""
        self.logger.info(f"Received /showcreds command from user {update.effective_user.id} in chat {update.effective_chat.id}")

        # Get profile name from arguments or use default
        profile = context.args[0] if context.args else "default"

        try:
            # Show credentials using bot.sh (this will need to be implemented)
            result = await self._execute_botsh_command([self.bot_script_path, "show-credentials", profile])

            if result[0] == 0:
                await update.message.reply_text(
                    f"🔐 <b>Thông tin tài khoản '{self.escape_html(profile)}':</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi hiển thị profile '{self.escape_html(profile)}':</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_addcreds_wizard(self, update: Update, context) -> None:
        """Handle /addcreds command - start credentials wizard"""
        user_id = update.effective_user.id
        
        self.session_manager.start_wizard(user_id, 'addcreds', {
            'step': 'profile_name',
            'data': {}
        })
        
        message_text = (
            "🔐 <b>Thêm thông tin tài khoản</b>\n\n"
            "Bước 1/4: Nhập tên profile (ví dụ: bybit_main, binance_test)\n\n"
            "💡 Tên profile giúp bạn phân biệt các tài khoản khác nhau.\n\n"
            "📝 Nhập tên profile hoặc `/cancel` để hủy:"
        )
        
        # Check if this is from callback query or regular message
        if update.callback_query:
            # From callback query (button click)
            query = update.callback_query
            await query.edit_message_text(message_text, parse_mode=ParseMode.HTML)
            # Send a new message for input since edited messages can't have reply_markup
            await query.message.reply_text(
                "👆 Nhập tên profile:",
                reply_markup=ForceReply(selective=True)
            )
        else:
            # From regular message command
            await update.message.reply_text(
                message_text,
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )

    # Bot Management Commands
    async def handle_createbot_wizard(self, update: Update, context) -> None:
        """Handle /createbot command - start createbot wizard"""
        user_id = update.effective_user.id
        
        # Clear any existing wizard
        self.session_manager.clear_session(user_id)
        
        # Check prerequisites
        if not await self._check_prerequisites():
            error_text = (
                "❌ <b>Không đủ điều kiện tạo bot</b>\n\n"
                "Vui lòng:\n"
                "1. Thêm thông tin tài khoản với <code>/addcreds</code> hoặc <code>/setkey</code>\n"
                "2. Đảm bảo Docker đang chạy\n\n"
                "Thử lại sau khi hoàn thành các bước trên."
            )
            
            if update.callback_query:
                query = update.callback_query
                await query.edit_message_text(error_text, parse_mode=ParseMode.HTML)
            else:
                await update.message.reply_text(error_text, parse_mode=ParseMode.HTML)
            return
        
        self.session_manager.start_wizard(user_id, 'createbot', {
            'step': 'symbol',
            'data': {}
        })
        
        message_text = (
            "🤖 <b>Tạo bot trading</b>\n\n"
            "Bước 1/3: Nhập symbol để trade\n\n"
            "Ví dụ:\n"
            "• `BTCUSDT` (Bitcoin/USDT)\n"
            "• `ETHUSDT` (Ethereum/USDT)\n"
            "• `HYPER` (Hyperliquid)\n\n"
            "💡 Hỗ trợ định dạng đơn giản (btc, eth) hoặc đầy đủ (BTC/USDT:USDT)\n\n"
            "📝 Nhập symbol hoặc `/cancel` để hủy:"
        )
        
        # Check if this is from callback query or regular message
        if update.callback_query:
            # From callback query (button click)
            query = update.callback_query
            await query.edit_message_text(message_text, parse_mode=ParseMode.HTML)
            # Send a new message for input since edited messages can't have reply_markup
            await query.message.reply_text(
                "👆 Nhập symbol:",
                reply_markup=ForceReply(selective=True)
            )
        else:
            # From regular message command
            await update.message.reply_text(
                message_text,
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )

    async def handle_startbot(self, update: Update, context) -> None:
        """Handle /startbot command"""
        chat_id = update.effective_chat.id
        
        if len(context.args) < 2:
            usage_msg = """
🚀 <b>Khởi động bot nhanh</b>

Cú pháp: <code>/startbot &lt;symbol&gt; &lt;amount&gt; [options]</code>

<b>Ví dụ:</b>
• <code>/startbot BTCUSDT 50</code> - Trade BTC với $50
• <code>/startbot ETHUSDT 100 test</code> - Test mode với $100
• <code>/startbot HYPER 25</code> - Trade HYPER với $25

💡 Sử dụng <code>/createbot</code> để setup chi tiết hơn.
            """
            
            await update.message.reply_text(usage_msg, parse_mode=ParseMode.HTML)
            return
        
        symbol = context.args[0]
        amount = context.args[1]
        
        # Validate inputs
        if not ValidationUtils.validate_amount(amount):
            await update.message.reply_text(
                "❌ <b>Số tiền không hợp lệ</b>\n\nVui lòng nhập số dương.",
                parse_mode=ParseMode.HTML
            )
            return
        
        try:
            # Build command arguments
            cmd_args = [self.bot_script_path, "start", symbol, "--amount", amount]
            
            # Add additional arguments
            for arg in context.args[2:]:
                if arg.lower() in ["test", "testmode", "test_mode"]:
                    cmd_args.append("--test-mode")
                else:
                    cmd_args.append(arg)
            
            # Start bot using bot.sh
            result = await self._execute_botsh_command(cmd_args)
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🚀 <b>Bot đã khởi động!</b>\n\n"
                    f"Symbol: <code>{self.escape_html(symbol)}</code>\n"
                    f"Amount: <code>${self.escape_html(amount)}</code>\n\n"
                    f"Sử dụng <code>/status {self.escape_html(symbol)}</code> để xem trạng thái.",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi khởi động:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_list(self, update: Update, context) -> None:
        """Handle /list command"""
        try:
            # Get container list using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list"])
            
            if result[0] == 0:
                if result[1].strip():
                    await update.message.reply_text(
                        f"📋 <b>Danh sách bot:</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                        parse_mode=ParseMode.HTML
                    )
                else:
                    await update.message.reply_text(
                        "📋 <b>Không có bot nào đang chạy</b>\n\n"
                        "Sử dụng <code>/createbot</code> để tạo bot mới.",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_status(self, update: Update, context) -> None:
        """Handle /status command"""
        if not context.args:
            await update.message.reply_text(
                "📊 <b>Xem trạng thái bot</b>\n\n"
                "Cú pháp: <code>/status &lt;symbol&gt;</code>\n\n"
                "Ví dụ: <code>/status BTCUSDT</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        
        try:
            # Get status using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "status", symbol])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"📊 <b>Trạng thái {self.escape_html(symbol)}:</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Không tìm thấy bot cho symbol '{self.escape_html(symbol)}'</b>\n\n"
                    "Sử dụng <code>/list</code> để xem tất cả bot.",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_logs(self, update: Update, context) -> None:
        """Handle /logs command"""
        if not context.args:
            await update.message.reply_text(
                "📜 <b>Xem log bot</b>\n\n"
                "Cú pháp: <code>/logs &lt;symbol&gt; [lines]</code>\n\n"
                "Ví dụ:\n"
                "• <code>/logs BTCUSDT</code> - 50 dòng cuối\n"
                "• <code>/logs BTCUSDT 20</code> - 20 dòng cuối",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        lines = context.args[1] if len(context.args) > 1 else "50"
        
        try:
            # Get logs using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "logs", symbol, lines])
            
            if result[0] == 0:
                if result[1].strip():
                                    await update.message.reply_text(
                    f"📜 <b>Log {self.escape_html(symbol)} ({lines} dòng):</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                else:
                    await update.message.reply_text(
                        f"📜 <b>Không có log cho <code>{self.escape_html(symbol)}</code></b>",
                        parse_mode=ParseMode.HTML
                    )
            else:
                await update.message.reply_text(
                    f"❌ <b>Không tìm thấy bot cho symbol '<code>{self.escape_html(symbol)}</code>'</b>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )
    
    async def handle_stopbot(self, update: Update, context) -> None:
        """Handle /stop command"""
        if not context.args:
            await update.message.reply_text(
                "⏹️ <b>Dừng bot</b>\n\n"
                "Cú pháp: <code>/stop &lt;symbol&gt;</code>\n\n"
                "Ví dụ: <code>/stop BTCUSDT</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        
        # Send confirmation request
        keyboard = [
            [InlineKeyboardButton("✅ Xác nhận", callback_data=f"stop_confirm_{symbol}"),
             InlineKeyboardButton("❌ Hủy", callback_data="stop_cancel")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            f"⏹️ <b>Xác nhận dừng bot</b>\n\n"
            f"Bạn có chắc muốn dừng bot <code>{self.escape_html(symbol)}</code>?\n\n"
            "⚠️ Điều này sẽ dừng tất cả hoạt động giao dịch.",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )
    
    async def handle_restart(self, update: Update, context) -> None:
        """Handle /restart command"""
        if not context.args:
            await update.message.reply_text(
                "🔄 <b>Khởi động lại bot</b>\n\n"
                "Cú pháp: <code>/restart &lt;symbol&gt;</code>\n\n"
                "Ví dụ: <code>/restart BTCUSDT</code>",
                parse_mode=ParseMode.HTML
            )
            return
        
        symbol = context.args[0]
        
        try:
            # Restart using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "restart", symbol])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"🔄 <b>Bot đã khởi động lại!</b>\n\n"
                    f"Symbol: <code>{self.escape_html(symbol)}</code>\n\n"
                    f"Sử dụng <code>/status {self.escape_html(symbol)}</code> để xem trạng thái.",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi khởi động lại:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_stopall(self, update: Update, context) -> None:
        """Handle /stopall command"""
        # Create confirmation keyboard
        keyboard = [
            [InlineKeyboardButton("✅ Xác nhận", callback_data="confirm_stopall")],
            [InlineKeyboardButton("❌ Hủy", callback_data="cancel_stopall")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            "⚠️ <b>Dừng tất cả bot</b>\n\n"
            "Bạn có chắc chắn muốn dừng TẤT CẢ bot đang chạy?\n\n"
            "⚠️ Điều này sẽ dừng tất cả hoạt động giao dịch.",
            parse_mode=ParseMode.HTML,
            reply_markup=reply_markup
        )

    # Config Management Commands
    async def handle_createconfig(self, update: Update, context) -> None:
        """Handle /createconfig command"""
        await update.message.reply_text(
            "⚙️ <b>Tạo cấu hình mới</b>\n\n"
            "Tính năng này đang được phát triển.\n"
            "Hiện tại bạn có thể sử dụng file config trong thư mục `configs/`",
            parse_mode=ParseMode.HTML
        )

    async def handle_listconfigs(self, update: Update, context) -> None:
        """Handle /listconfigs command"""
        try:
            # List config files using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "list-configs"])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📁 <b>Danh sách cấu hình:</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def handle_showconfig(self, update: Update, context) -> None:
        """Handle /showconfig command"""
        if not context.args:
            await update.message.reply_text(
                "📄 <b>Xem cấu hình</b>\n\n"
                "Cú pháp: <code>/showconfig &lt;config_name&gt;</code>\n\n"
                "Ví dụ: <code>/showconfig default</code>",
                parse_mode=ParseMode.HTML
            )
            return

        config_name = context.args[0]

        try:
            # Show config using bot.sh
            result = await self._execute_botsh_command([self.bot_script_path, "show-config", config_name])

            if result[0] == 0:
                await update.message.reply_text(
                    f"📄 <b>Cấu hình '{self.escape_html(config_name)}':</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    # Notification Commands
    async def handle_subscribe(self, update: Update, context) -> None:
        """Handle /subscribe command"""
        chat_id = update.effective_chat.id

        # Simple subscription toggle
        await update.message.reply_text(
            "🔔 <b>Đăng ký thông báo</b>\n\n"
            "✅ Bạn đã đăng ký nhận thông báo:\n"
            "• Thông báo giao dịch\n"
            "• Cảnh báo lỗi\n"
            "• Cập nhật trạng thái bot\n\n"
            "Sử dụng <code>/unsubscribe</code> để hủy đăng ký.",
            parse_mode=ParseMode.HTML
        )

    async def handle_unsubscribe(self, update: Update, context) -> None:
        """Handle /unsubscribe command"""
        chat_id = update.effective_chat.id

        await update.message.reply_text(
            "🔕 <b>Hủy đăng ký thông báo</b>\n\n"
            "❌ Bạn đã hủy đăng ký thông báo.\n\n"
            "Sử dụng <code>/subscribe</code> để đăng ký lại.",
            parse_mode=ParseMode.HTML
        )

    async def handle_alerts(self, update: Update, context) -> None:
        """Handle /alerts command"""
        await update.message.reply_text(
            "⚠️ <b>Cấu hình cảnh báo</b>\n\n"
            "Tính năng này đang được phát triển.\n\n"
            "Hiện tại bot sẽ tự động gửi thông báo khi:\n"
            "• Có giao dịch mới\n"
            "• Bot gặp lỗi\n"
            "• Thay đổi trạng thái bot",
            parse_mode=ParseMode.HTML
        )

    async def handle_test_notification(self, update: Update, context) -> None:
        """Handle /testnotify command - test trading notifications"""
        try:
            # Initialize notification manager if not done
            if not hasattr(self.notification_manager, 'bot') or not self.notification_manager.bot:
                await self.notification_manager.initialize()

            # Test different types of notifications
            test_type = context.args[0] if context.args else "trade"

            if test_type == "trade":
                await self.notification_manager.send_trade_notification({
                    'symbol': 'BTC/USDT',
                    'side': 'BUY',
                    'amount': 100.0,
                    'price': 45000.0,
                    'timestamp': '14:30:25'
                })
                await update.message.reply_text("✅ Test trade notification sent!")

            elif test_type == "position":
                await self.notification_manager.send_position_update({
                    'symbol': 'ETH/USDT',
                    'side': 'LONG',
                    'size': 250.0,
                    'unrealized_pnl': 15.50,
                    'pnl_percentage': 2.3
                })
                await update.message.reply_text("✅ Test position notification sent!")

            elif test_type == "error":
                await self.notification_manager.send_error_alert({
                    'type': 'Connection Error',
                    'message': 'Failed to connect to exchange API',
                    'bot_name': 'BTC-USDT Bot',
                    'timestamp': '14:30:25'
                })
                await update.message.reply_text("✅ Test error notification sent!")

            elif test_type == "profit":
                await self.notification_manager.send_profit_alert({
                    'profit_amount': 125.50,
                    'profit_percentage': 8.5,
                    'total_trades': 15,
                    'win_rate': 73.3
                })
                await update.message.reply_text("✅ Test profit notification sent!")

            else:
                await update.message.reply_text(
                    "🧪 <b>Test Notifications</b>\n\n"
                    "Usage: <code>/testnotify [type]</code>\n\n"
                    "Types:\n"
                    "• <code>trade</code> - Trade execution\n"
                    "• <code>position</code> - Position update\n"
                    "• <code>error</code> - Error alert\n"
                    "• <code>profit</code> - Profit milestone",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi test notification:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    # Callback Query Handler
    async def handle_callback_query(self, update: Update, context) -> None:
        """Handle inline keyboard callbacks"""
        query = update.callback_query
        await query.answer()
        
        data = query.data
        user_id = query.from_user.id
        chat_id = query.message.chat.id
        
        if data == "help_guide":
            await self.handle_help(update, context)
        elif data == "start_addcreds":
            await self.handle_addcreds_wizard(update, context)
        elif data == "start_createbot":
            await self.handle_createbot_wizard(update, context)
        elif data.startswith("stop_confirm_"):
            symbol = data.replace("stop_confirm_", "")
            await self._handle_stop_confirm(query, symbol)
        elif data == "stop_cancel":
            await query.edit_message_text("❌ <b>Đã hủy dừng bot</b>", parse_mode=ParseMode.HTML)
        elif data == "confirm_stopall":
            await self._handle_stopall_confirm(query)
        elif data == "cancel_stopall":
            await query.edit_message_text("❌ <b>Đã hủy dừng tất cả bot</b>", parse_mode=ParseMode.HTML)
        else:
            await query.edit_message_text("❌ <b>Lệnh không xác định</b>", parse_mode=ParseMode.HTML)

    async def _handle_stop_confirm(self, query, symbol: str):
        """Handle stop confirmation"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "stop", symbol])
            
            if result[0] == 0:
                await query.edit_message_text(
                    f"✅ <b>Bot đã dừng!</b>\n\n"
                    f"Symbol: <code>{self.escape_html(symbol)}</code>\n\n"
                    f"Sử dụng <code>/startbot {self.escape_html(symbol)} &lt;amount&gt;</code> để khởi động lại.",
                    parse_mode=ParseMode.HTML
                )
            else:
                await query.edit_message_text(
                    f"❌ <b>Lỗi dừng bot:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await query.edit_message_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def _handle_stopall_confirm(self, query):
        """Handle stop all confirmation"""
        try:
            result = await self._execute_botsh_command([self.bot_script_path, "stop-all"])

            if result[0] == 0:
                await query.edit_message_text(
                    f"⏹️ <b>Đã dừng tất cả bot</b>\n\n<pre>{self.escape_html(result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await query.edit_message_text(
                    f"❌ <b>Lỗi dừng tất cả bot:</b> <pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )

        except Exception as e:
            await query.edit_message_text(
                f"❌ <b>Lỗi dừng tất cả bot:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    # Message Handler for Wizards
    async def handle_wizard_message(self, update: Update, context) -> None:
        """Handle wizard input messages"""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            # No active wizard, ignore
            return
        
        session = self.session_manager.get_session(user_id)
        wizard_type = session['wizard_state']

        # Check for cancel command
        if update.message.text.strip().lower() == '/cancel':
            await self.handle_cancel(update, context)
            return

        if wizard_type == 'addcreds':
            await self._handle_addcreds_step(update, context, session)
        elif wizard_type == 'createbot':
            await self._handle_createbot_step(update, context, session)

    async def _handle_addcreds_step(self, update: Update, context, session: Dict) -> None:
        """Handle steps in addcreds wizard"""
        user_id = update.effective_user.id
        step = session['wizard_data']['step']
        data = session['wizard_data']['data']
        text = update.message.text.strip()
        
        if step == 'profile_name':
            if not ValidationUtils.validate_profile_name(text):
                await update.message.reply_text(
                    "❌ <b>Tên profile không hợp lệ</b>\n\n"
                    "Tên chỉ được chứa chữ cái, số và dấu gạch dưới.",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['profile'] = text
            self.session_manager.update_wizard_data(user_id, 'profile', text)
            self.session_manager.update_wizard_data(user_id, 'step', 'api_key')
            
            await update.message.reply_text(
                "🔑 <b>Bước 2/4: Nhập API Key</b>\n\n"
                "Dán API Key từ exchange của bạn:\n\n"
                "📝 Nhập API Key hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'api_key':
            if not ValidationUtils.validate_api_key(text):
                await update.message.reply_text(
                    "❌ <b>API Key không hợp lệ</b>\n\n"
                    "Key phải có ít nhất 10 ký tự.",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['api_key'] = text
            self.session_manager.update_wizard_data(user_id, 'api_key', text)
            self.session_manager.update_wizard_data(user_id, 'step', 'api_secret')
            
            await update.message.reply_text(
                "🔐 <b>Bước 3/4: Nhập API Secret</b>\n\n"
                "Dán API Secret từ exchange của bạn:\n\n"
                "📝 Nhập API Secret hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'api_secret':
            if not ValidationUtils.validate_api_key(text):  # Same validation as API key
                await update.message.reply_text(
                    "❌ <b>API Secret không hợp lệ</b>\n\n"
                    "Secret phải có ít nhất 10 ký tự.",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['api_secret'] = text
            self.session_manager.update_wizard_data(user_id, 'api_secret', text)
            self.session_manager.update_wizard_data(user_id, 'step', 'description')
            
            await update.message.reply_text(
                "📝 <b>Bước 4/4: Nhập mô tả (tùy chọn)</b>\n\n"
                "Mô tả ngắn cho tài khoản này, hoặc gửi 'skip' để bỏ qua:\n\n"
                "📝 Nhập mô tả, 'skip' để bỏ qua, hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'description':
            if text.lower() == 'skip':
                data['description'] = f"Profile {data['profile']}"
            else:
                data['description'] = text
            
            # Save credentials
            await self._save_credentials(update, data)
            self.session_manager.clear_session(user_id)

    async def _save_credentials(self, update: Update, data: Dict):
        """Save credentials using bot.sh"""
        try:
            result = await self._execute_botsh_command([
                self.bot_script_path, "store-credentials", 
                data['profile'], data['api_key'], data['api_secret'], data['description']
            ])
            
            if result[0] == 0:
                await update.message.reply_text(
                    f"✅ <b>Lưu thành công!</b>\n\n"
                    f"Profile: <code>{self.escape_html(data['profile'])}</code>\n"
                    f"Mô tả: {self.escape_html(data['description'])}\n\n"
                    "Bây giờ bạn có thể tạo bot với <code>/createbot</code>",
                    parse_mode=ParseMode.HTML
                )
            else:
                await update.message.reply_text(
                    f"❌ <b>Lỗi lưu credentials:</b>\n<pre>{self.escape_html(result[2] or result[1])}</pre>",
                    parse_mode=ParseMode.HTML
                )
                
        except Exception as e:
            await update.message.reply_text(
                f"❌ <b>Lỗi:</b> <pre>{self.escape_html(str(e))}</pre>",
                parse_mode=ParseMode.HTML
            )

    async def _handle_createbot_step(self, update: Update, context, session: Dict) -> None:
        """Handle steps in createbot wizard"""
        user_id = update.effective_user.id
        step = session['wizard_data']['step']
        data = session['wizard_data']['data']
        text = update.message.text.strip()
        
        if step == 'symbol':
            if not ValidationUtils.validate_symbol(text):
                await update.message.reply_text(
                    "❌ <b>Symbol không hợp lệ</b>\n\n"
                    "Ví dụ: BTCUSDT, ETHUSDT, HYPER",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['symbol'] = text.upper()
            session['wizard_data']['step'] = 'amount'
            self.session_manager.update_wizard_data(user_id, 'step', 'amount')
            
            await update.message.reply_text(
                f"💰 <b>Bước 2/3: Nhập số tiền trade</b>\n\n"
                f"Symbol: <code>{self.escape_html(data['symbol'])}</code>\n\n"
                "Nhập số tiền (USD) để trade:\n\n"
                "📝 Nhập số tiền hoặc `/cancel` để hủy:",
                parse_mode=ParseMode.HTML,
                reply_markup=ForceReply(selective=True)
            )
            
        elif step == 'amount':
            if not ValidationUtils.validate_amount(text):
                await update.message.reply_text(
                    "❌ <b>Số tiền không hợp lệ</b>\n\n"
                    "Vui lòng nhập số dương (ví dụ: 100, 50.5)",
                    parse_mode=ParseMode.HTML
                )
                return
            
            data['amount'] = text
            session['wizard_data']['step'] = 'confirm'
            self.session_manager.update_wizard_data(user_id, 'step', 'confirm')
            
            # Show confirmation
            keyboard = [
                [InlineKeyboardButton("✅ Xác nhận", callback_data="createbot_confirm"),
                 InlineKeyboardButton("❌ Hủy", callback_data="createbot_cancel")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                f"🤖 <b>Bước 3/3: Xác nhận tạo bot</b>\n\n"
                f"Symbol: <code>{self.escape_html(data['symbol'])}</code>\n"
                f"Amount: <code>${self.escape_html(data['amount'])}</code>\n\n"
                "Xác nhận tạo bot?",
                parse_mode=ParseMode.HTML,
                reply_markup=reply_markup
            )

    async def _check_prerequisites(self) -> bool:
        """Check if prerequisites for bot creation are met"""
        try:
            # Check if credentials exist
            result = await self._execute_botsh_command([self.bot_script_path, "list-credentials"])

            # Log for debugging
            self.logger.info(f"Credentials check - Return code: {result[0]}")
            self.logger.info(f"Credentials check - Output: {repr(result[1])}")

            # Check if command failed
            if result[0] != 0:
                self.logger.warning("Credentials check failed - command returned non-zero")
                return False

            # Check if output is empty
            if not result[1].strip():
                self.logger.warning("Credentials check failed - empty output")
                return False

            # Check if no credentials found
            if "No credential profiles found" in result[1] or "📭 No credential profiles found" in result[1]:
                self.logger.warning("Credentials check failed - no profiles found")
                return False

            # Check if Docker is running (simple check)
            import subprocess
            try:
                subprocess.run(["docker", "info"], check=True, capture_output=True)
                self.logger.info("Docker check passed")
                return True
            except (subprocess.CalledProcessError, FileNotFoundError) as e:
                self.logger.warning(f"Docker check failed: {e}")
                return False

        except Exception as e:
            self.logger.error(f"Prerequisites check failed with exception: {e}")
            return False

    async def _execute_botsh_command(self, command: List[str]) -> Tuple[int, str, str]:
        """Execute bot.sh command and return (exit_code, stdout, stderr)"""
        import subprocess
        import asyncio
        
        try:
            # Run command asynchronously
            process = await asyncio.create_subprocess_exec(
                *command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="."
            )
            
            stdout, stderr = await process.communicate()
            
            return (
                process.returncode or 0,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )
            
        except Exception as e:
            self.logger.error(f"Error executing command {' '.join(command)}: {e}")
            return (1, "", str(e)) 