"""Bot Operations Handler - Handle Docker container operations (start, stop, restart)"""
import subprocess
import asyncio
from typing import Dict, List, Optional, Any
from ..telegram_base import TelegramBaseHandler


class BotOperationsHandler(TelegramBaseHandler):
    """Handles Docker container operations for trading bots"""
    
    def __init__(self, session_manager):
        super().__init__('BotOperationsHandler')
        self.session_manager = session_manager
    
    # ===============================
    # Individual Container Operations
    # ===============================
    
    async def handle_stop_command(self, update, context):
        """Handle /stop command - Stop a specific container"""
        try:
            if not context.args:
                await self._show_stop_help(update, context)
                return
            
            container_name = context.args[0]
            
            # Check if container exists
            if not await self._container_exists(container_name):
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ Container `{container_name}` not found.\n\n"
                    "Use `/list` to see available containers."
                )
                return
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("✅ Confirm Stop", f"op_stop_confirm_{container_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"⚠️ **Confirm Stop Operation**\n\n"
                f"Are you sure you want to stop container `{container_name}`?\n\n"
                f"📊 Use `/status {container_name}` to check current status first.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "stop")
            )
    
    async def handle_restart_command(self, update, context):
        """Handle /restart command - Restart a specific container"""
        try:
            if not context.args:
                await self._show_restart_help(update, context)
                return
            
            container_name = context.args[0]
            
            if not await self._container_exists(container_name):
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ Container `{container_name}` not found.\n\n"
                    "Use `/list` to see available containers."
                )
                return
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("🔄 Confirm Restart", f"op_restart_confirm_{container_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"🔄 **Confirm Restart Operation**\n\n"
                f"Container: `{container_name}`\n\n"
                f"This will stop and start the container again.\n"
                f"Any unsaved data may be lost.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "restart")
            )
    
    async def handle_startbot_command(self, update, context):
        """Handle /startbot command - Quick start existing bot"""
        try:
            # Quick start keyboard
            keyboard = self.create_keyboard([
                [("🔍 Select Container", "op_select_container"), ("📊 Show All", "bot_list")],
                [("🔄 Create New", "bot_create_start"), ("❌ Cancel", "close")]
            ])
            
            available_containers = await self._get_all_containers()
            stopped_containers = [c for c in available_containers if c['status'] != 'running']
            
            message = "🚀 **Quick Start Bot**\n\n"
            
            if stopped_containers:
                message += "**Stopped containers that can be started:**\n"
                for i, container in enumerate(stopped_containers[:5], 1):
                    message += f"{i}. `{container['name']}` - {container['status']}\n"
                
                if len(stopped_containers) > 5:
                    message += f"\n... and {len(stopped_containers) - 5} more\n"
                
                message += "\n💡 Use buttons below or `/start <container_name>`"
            else:
                message += "ℹ️ No stopped containers found.\n\n"
                message += "All containers are already running or you need to create a new bot."
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "startbot")
            )
    
    async def handle_start_specific_command(self, update, context):
        """Handle /start <container_name> command"""
        try:
            if not context.args:
                await self.handle_startbot_command(update, context)
                return
            
            container_name = context.args[0]
            
            if not await self._container_exists(container_name):
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ Container `{container_name}` not found.\n\n"
                    "Use `/list` to see available containers or `/startbot` for guided selection."
                )
                return
            
            # Check if already running
            status = await self._get_container_simple_status(container_name)
            if status == 'running':
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"ℹ️ Container `{container_name}` is already running.\n\n"
                    f"Use `/status {container_name}` to check details."
                )
                return
            
            # Start the container
            success = await self._start_container(container_name)
            
            if success:
                keyboard = self.create_keyboard([
                    [("📊 Check Status", f"bot_status_{container_name}"), ("📜 View Logs", f"bot_logs_{container_name}")],
                    [("❌ Close", "close")]
                ])
                
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ **Container Started Successfully**\n\n"
                    f"Container: `{container_name}`\n"
                    f"Status: Starting...\n\n"
                    f"💡 It may take a few moments to fully initialize.",
                    reply_markup=keyboard
                )
            else:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ **Failed to start container**\n\n"
                    f"Container: `{container_name}`\n\n"
                    f"Please check Docker logs for details."
                )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "start")
            )
    
    # ===============================
    # Bulk Operations
    # ===============================
    
    async def handle_stopall_command(self, update, context):
        """Handle /stopall command - Stop all running containers"""
        try:
            running_containers = await self._get_running_containers()
            
            if not running_containers:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "ℹ️ **No Running Containers**\n\n"
                    "There are no trading bot containers currently running.\n\n"
                    "Use `/list` to see all containers."
                )
                return
            
            # Show containers that will be stopped
            message = "⚠️ **Confirm Stop All Operation**\n\n"
            message += f"This will stop **{len(running_containers)}** running containers:\n\n"
            
            for i, container in enumerate(running_containers, 1):
                message += f"{i}. `{container['name']}`\n"
            
            message += "\n💥 **This action cannot be undone!**"
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("🛑 Stop All Containers", "op_stopall_confirm"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                message,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "stopall")
            )
    
    # ===============================
    # Help Commands
    # ===============================
    
    async def _show_stop_help(self, update, context):
        """Show help for stop command"""
        containers = await self._get_running_containers()
        
        message = "⏹️ **Stop Container Command**\n\n"
        message += "**Usage:** `/stop <container_name>`\n\n"
        
        if containers:
            message += "**Running containers:**\n"
            for container in containers:
                message += f"• `{container['name']}`\n"
            message += f"\n**Example:** `/stop {containers[0]['name']}`"
        else:
            message += "ℹ️ No containers are currently running."
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("🛑 Stop All", "op_stopall")],
            [("❌ Close", "close")]
        ])
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message,
            reply_markup=keyboard
        )
    
    async def _show_restart_help(self, update, context):
        """Show help for restart command"""
        containers = await self._get_all_containers()
        
        message = "🔄 **Restart Container Command**\n\n"
        message += "**Usage:** `/restart <container_name>`\n\n"
        
        if containers:
            message += "**Available containers:**\n"
            for container in containers:
                status_emoji = "🟢" if container['status'] == 'running' else "🔴"
                message += f"{status_emoji} `{container['name']}` - {container['status']}\n"
            message += f"\n**Example:** `/restart {containers[0]['name']}`"
        else:
            message += "ℹ️ No containers found."
        
        keyboard = self.create_keyboard([
            [("📋 List All", "bot_list"), ("❌ Close", "close")]
        ])
        
        await self.send_message(
            context.bot,
            update.effective_chat.id,
            message,
            reply_markup=keyboard
        )
    
    # ===============================
    # Docker Operations
    # ===============================
    
    async def _start_container(self, container_name: str) -> bool:
        """Start a specific container"""
        try:
            result = subprocess.run([
                'docker', 'start', container_name
            ], capture_output=True, text=True, check=True)
            
            self.logger.info(f"Started container: {container_name}")
            return True
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error starting container {container_name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error starting container {container_name}: {e}")
            return False
    
    async def _stop_container(self, container_name: str) -> bool:
        """Stop a specific container"""
        try:
            result = subprocess.run([
                'docker', 'stop', container_name
            ], capture_output=True, text=True, check=True, timeout=30)
            
            self.logger.info(f"Stopped container: {container_name}")
            return True
            
        except subprocess.TimeoutExpired:
            # Force stop if graceful stop takes too long
            try:
                subprocess.run(['docker', 'kill', container_name], check=True)
                self.logger.warning(f"Force killed container: {container_name}")
                return True
            except:
                return False
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error stopping container {container_name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error stopping container {container_name}: {e}")
            return False
    
    async def _restart_container(self, container_name: str) -> bool:
        """Restart a specific container"""
        try:
            result = subprocess.run([
                'docker', 'restart', container_name
            ], capture_output=True, text=True, check=True, timeout=60)
            
            self.logger.info(f"Restarted container: {container_name}")
            return True
            
        except subprocess.TimeoutExpired:
            self.logger.error(f"Timeout restarting container: {container_name}")
            return False
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error restarting container {container_name}: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error restarting container {container_name}: {e}")
            return False
    
    async def _stop_all_containers(self) -> Dict[str, bool]:
        """Stop all running containers"""
        containers = await self._get_running_containers()
        results = {}
        
        for container in containers:
            container_name = container['name']
            self.logger.info(f"Stopping container: {container_name}")
            results[container_name] = await self._stop_container(container_name)
            
            # Small delay between stops
            await asyncio.sleep(1)
        
        return results
    
    async def _container_exists(self, container_name: str) -> bool:
        """Check if container exists"""
        try:
            result = subprocess.run([
                'docker', 'inspect', container_name
            ], capture_output=True, text=True, check=True)
            return True
        except subprocess.CalledProcessError:
            return False
    
    async def _get_container_simple_status(self, container_name: str) -> Optional[str]:
        """Get simple status (running/stopped) of container"""
        try:
            result = subprocess.run([
                'docker', 'inspect', '--format={{.State.Status}}', container_name
            ], capture_output=True, text=True, check=True)
            
            return result.stdout.strip()
        except subprocess.CalledProcessError:
            return None
    
    async def _get_running_containers(self) -> List[Dict]:
        """Get list of running containers"""
        try:
            result = subprocess.run([
                'docker', 'ps', '--format',
                'table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}'
            ], capture_output=True, text=True, check=True)
            
            containers = []
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        containers.append({
                            'name': parts[0].strip(),
                            'status': parts[1].strip(),
                            'created': parts[2].strip()
                        })
            
            return containers
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting running containers: {e}")
            return []
    
    async def _get_all_containers(self) -> List[Dict]:
        """Get list of all containers (running and stopped)"""
        try:
            result = subprocess.run([
                'docker', 'ps', '-a', '--format',
                'table {{.Names}}\t{{.Status}}\t{{.CreatedAt}}'
            ], capture_output=True, text=True, check=True)
            
            containers = []
            lines = result.stdout.strip().split('\n')[1:]  # Skip header
            
            for line in lines:
                if line.strip():
                    parts = line.split('\t')
                    if len(parts) >= 3:
                        containers.append({
                            'name': parts[0].strip(),
                            'status': parts[1].strip().split()[0],  # Get just the status word
                            'created': parts[2].strip()
                        })
            
            return containers
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Error getting all containers: {e}")
            return []
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Handle command - Router for bot operations commands"""
        command = update.message.text.split()[0].lower()
        
        if command == '/stop':
            await self.handle_stop_command(update, context)
        elif command == '/restart':
            await self.handle_restart_command(update, context)
        elif command == '/startbot':
            await self.handle_startbot_command(update, context)
        elif command == '/stopall':
            await self.handle_stopall_command(update, context)
        else:
            # Fallback - should not reach here normally
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"❓ Command `{command}` not handled by BotOperationsHandler"
            )
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for bot operations"""
        try:
            if data.startswith("op_stop_confirm_"):
                container_name = data.replace("op_stop_confirm_", "")
                await query.edit_message_text(f"🔄 Stopping container `{container_name}`...")
                
                success = await self._stop_container(container_name)
                
                if success:
                    keyboard = self.create_keyboard([
                        [("📊 Check Status", f"bot_status_{container_name}"), ("🚀 Start Again", f"op_start_{container_name}")],
                        [("❌ Close", "close")]
                    ])
                    
                    await query.edit_message_text(
                        f"✅ **Container Stopped Successfully**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Status: Stopped\n\n"
                        f"Use 🚀 Start Again to restart when needed.",
                        reply_markup=keyboard
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Failed to stop container**\n\n"
                        f"Container: `{container_name}`\n\n"
                        f"Please check Docker status manually."
                    )
                
                return True
            
            elif data.startswith("op_restart_confirm_"):
                container_name = data.replace("op_restart_confirm_", "")
                await query.edit_message_text(f"🔄 Restarting container `{container_name}`...")
                
                success = await self._restart_container(container_name)
                
                if success:
                    keyboard = self.create_keyboard([
                        [("📊 Check Status", f"bot_status_{container_name}"), ("📜 View Logs", f"bot_logs_{container_name}")],
                        [("❌ Close", "close")]
                    ])
                    
                    await query.edit_message_text(
                        f"✅ **Container Restarted Successfully**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Status: Restarting...\n\n"
                        f"💡 It may take a few moments to fully initialize.",
                        reply_markup=keyboard
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Failed to restart container**\n\n"
                        f"Container: `{container_name}`\n\n"
                        f"Please check Docker status manually."
                    )
                
                return True
            
            elif data == "op_stopall_confirm":
                await query.edit_message_text("🔄 Stopping all containers...")
                
                results = await self._stop_all_containers()
                
                total = len(results)
                successful = sum(1 for success in results.values() if success)
                failed = total - successful
                
                message = f"📊 **Stop All Results**\n\n"
                message += f"Total containers: {total}\n"
                message += f"✅ Successfully stopped: {successful}\n"
                message += f"❌ Failed to stop: {failed}\n\n"
                
                if failed > 0:
                    message += "**Failed containers:**\n"
                    for container_name, success in results.items():
                        if not success:
                            message += f"• `{container_name}`\n"
                
                keyboard = self.create_keyboard([
                    [("📋 List All", "bot_list"), ("❌ Close", "close")]
                ])
                
                await query.edit_message_text(message, reply_markup=keyboard)
                return True
            
            elif data.startswith("op_start_"):
                container_name = data.replace("op_start_", "")
                await query.edit_message_text(f"🚀 Starting container `{container_name}`...")
                
                success = await self._start_container(container_name)
                
                if success:
                    keyboard = self.create_keyboard([
                        [("📊 Check Status", f"bot_status_{container_name}"), ("📜 View Logs", f"bot_logs_{container_name}")],
                        [("❌ Close", "close")]
                    ])
                    
                    await query.edit_message_text(
                        f"✅ **Container Started Successfully**\n\n"
                        f"Container: `{container_name}`\n"
                        f"Status: Starting...\n\n"
                        f"💡 It may take a few moments to fully initialize.",
                        reply_markup=keyboard
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Failed to start container**\n\n"
                        f"Container: `{container_name}`\n\n"
                        f"Please check Docker logs for details."
                    )
                
                return True
            
            elif data == "op_select_container":
                stopped_containers = [c for c in await self._get_all_containers() if c['status'] != 'running']
                
                if not stopped_containers:
                    await query.edit_message_text(
                        "ℹ️ No stopped containers available to start.\n\n"
                        "All containers are running or you need to create a new bot."
                    )
                    return True
                
                # Create selection keyboard
                keyboard_buttons = []
                for container in stopped_containers[:8]:  # Limit to 8 for clean UI
                    keyboard_buttons.append([(f"▶️ {container['name']}", f"op_start_{container['name']}")])
                
                keyboard_buttons.append([("❌ Cancel", "close")])
                keyboard = self.create_keyboard(keyboard_buttons)
                
                message = "🔍 **Select Container to Start**\n\n"
                for i, container in enumerate(stopped_containers, 1):
                    message += f"{i}. `{container['name']}` - {container['status']}\n"
                
                await query.edit_message_text(message, reply_markup=keyboard)
                return True
            
            elif data == "op_stopall":
                # Redirect to stopall command
                await self.handle_stopall_command(query, None)
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error in callback handler: {e}")
            await query.edit_message_text(f"❌ Error: {str(e)}")
            return True 