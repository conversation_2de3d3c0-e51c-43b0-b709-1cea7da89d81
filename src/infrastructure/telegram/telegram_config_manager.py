"""Telegram Config Manager - Handle trading config files via Telegram"""
from typing import Dict, List, Optional, Any, Tuple
from .telegram_base import TelegramBaseHandler, ValidationUtils, MessageFormatter

try:
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TelegramConfigManager(TelegramBaseHandler):
    """Manages trading configuration files through Telegram interface"""
    
    def __init__(self, session_manager):
        super().__init__('TelegramConfigManager')
        self.session_manager = session_manager
    
    # ===============================
    # Config File Operations
    # ===============================
    
    async def handle_listconfigs_command(self, update, context):
        """Handle /listconfigs command - List available config files"""
        try:
            command = ["./bot.sh", "config", "list"]
            response = await self.execute_botsh_with_response(command)
            
            keyboard = self.create_keyboard([
                [("➕ Create New", "config_create"), ("📝 Edit", "config_edit_select")],
                [("🔄 Refresh", "config_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"📁 **Available Config Files**\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "listconfigs")
            )
    
    async def handle_showconfig_command(self, update, context):
        """Handle /showconfig command - Show specific config details"""
        try:
            config_name = context.args[0] if context.args else "config"
            
            command = ["./bot.sh", "config", "show", config_name]
            response = await self.execute_botsh_with_response(command)
            
            keyboard = self.create_keyboard([
                [("✏️ Edit", f"config_edit_{config_name}"), ("📋 List All", "config_list")],
                [("🔄 Refresh", f"config_show_{config_name}"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"⚙️ **Config: {config_name}**\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "showconfig")
            )
    
    async def handle_createconfig_wizard(self, update, context):
        """Handle /createconfig command - Start config creation wizard"""
        try:
            user_id = update.effective_user.id
            
            # Clear any existing wizard
            self.session_manager.clear_session(user_id)
            
            # Start wizard
            self.session_manager.start_wizard(user_id, 'create_config')
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                "⚙️ **Create New Config Wizard**\n\n"
                "Step 1/6: What would you like to name this config?\n\n"
                "Examples: `scalping`, `dca_strategy`, `test_config`\n\n"
                "📝 Enter config name (or /cancel to abort):"
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "createconfig")
            )
    
    async def handle_editconfig_command(self, update, context):
        """Handle /editconfig command - Edit specific config parameter"""
        try:
            if len(context.args) < 3:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "✏️ **Edit Config Parameter**\n\n"
                    "Usage: `/editconfig <config> <parameter> <value>`\n\n"
                    "Examples:\n"
                    "• `/editconfig test symbol HYPER/USDT:USDT`\n"
                    "• `/editconfig main amount 50`\n"
                    "• `/editconfig scalping stop_loss 5`\n\n"
                    "Use `/listconfigs` to see available configs."
                )
                return
            
            config_name, parameter, value = context.args[0], context.args[1], context.args[2]
            
            # Validate parameter
            valid_params = ['symbol', 'amount', 'direction', 'stop_loss', 'take_profit', 
                          'dca_amount', 'dca_bb_amount', 'dca_ema_amount', 'timeframe']
            
            if parameter not in valid_params:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ Invalid parameter: `{parameter}`\n\n"
                    f"Valid parameters: {', '.join(valid_params)}"
                )
                return
            
            # Execute edit command
            command = ["./bot.sh", "config", "edit", config_name, parameter, value]
            response = await self.execute_botsh_with_response(command)
            
            keyboard = self.create_keyboard([
                [("📊 Show Config", f"config_show_{config_name}"), ("📋 List All", "config_list")],
                [("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"✏️ **Config Updated**\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "editconfig")
            )
    
    async def handle_deleteconfig_command(self, update, context):
        """Handle /deleteconfig command - Delete config file"""
        try:
            if not context.args:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    "🗑️ **Delete Config File**\n\n"
                    "Usage: `/deleteconfig <config_name>`\n\n"
                    "Example: `/deleteconfig test_config`\n\n"
                    "⚠️ This cannot be undone!"
                )
                return
            
            config_name = context.args[0]
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("✅ Yes, Delete", f"config_confirm_delete_{config_name}"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"⚠️ **Confirm Deletion**\n\n"
                f"Are you sure you want to delete config: `{config_name}`?\n\n"
                f"This action cannot be undone!",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "deleteconfig")
            )
    
    # ===============================
    # Config Templates
    # ===============================
    
    async def handle_templates_command(self, update, context):
        """Handle /templates command - Show available config templates"""
        try:
            templates_info = (
                "📋 **Available Config Templates**\n\n"
                "🎯 **Scalping Template**\n"
                "• Quick trades, tight stop-loss\n"
                "• High frequency, small profits\n"
                "• Command: `/template scalping`\n\n"
                "📈 **DCA Template**\n"
                "• Dollar Cost Averaging strategy\n"
                "• Multiple buy levels\n"
                "• Command: `/template dca`\n\n"
                "⚡ **Grid Template**\n"
                "• Grid trading strategy\n"
                "• Buy/sell levels in range\n"
                "• Command: `/template grid`\n\n"
                "🧪 **Test Template**\n"
                "• Safe testing configuration\n"
                "• Paper trading enabled\n"
                "• Command: `/template test`"
            )
            
            keyboard = self.create_keyboard([
                [("🎯 Scalping", "config_template_scalping"), ("📈 DCA", "config_template_dca")],
                [("⚡ Grid", "config_template_grid"), ("🧪 Test", "config_template_test")],
                [("📋 List Configs", "config_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                templates_info,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "templates")
            )
    
    async def handle_template_command(self, update, context):
        """Handle /template command - Create config from template"""
        try:
            if not context.args:
                await self.handle_templates_command(update, context)
                return
            
            template_type = context.args[0].lower()
            config_name = context.args[1] if len(context.args) > 1 else f"{template_type}_config"
            
            valid_templates = ['scalping', 'dca', 'grid', 'test']
            if template_type not in valid_templates:
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"❌ Invalid template: `{template_type}`\n\n"
                    f"Available templates: {', '.join(valid_templates)}\n\n"
                    f"Use `/templates` to see details."
                )
                return
            
            # Create config from template
            command = ["./bot.sh", "config", "template", template_type, config_name]
            response = await self.execute_botsh_with_response(command)
            
            keyboard = self.create_keyboard([
                [("📊 Show Config", f"config_show_{config_name}"), ("✏️ Edit", f"config_edit_{config_name}")],
                [("📋 List All", "config_list"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                f"📋 **Template Created**\n\n{response}",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "template")
            )
    
    # ===============================
    # Wizard Input Handler
    # ===============================
    
    async def handle_wizard_input(self, update, context) -> bool:
        """Handle text input for config wizards. Returns True if handled."""
        user_id = update.effective_user.id
        
        if not self.session_manager.is_wizard_active(user_id):
            return False
        
        session = self.session_manager.get_session(user_id)
        wizard_state = session.get('wizard_state')
        
        if wizard_state != 'create_config':
            return False
        
        return await self._handle_create_config_input(update, context)
    
    async def _handle_create_config_input(self, update, context) -> bool:
        """Handle input for create config wizard"""
        user_id = update.effective_user.id
        text = update.message.text.strip()
        wizard_data = self.session_manager.get_wizard_data(user_id)
        
        try:
            # Step 1: Config name
            if 'config_name' not in wizard_data:
                if not text.replace('_', '').replace('-', '').isalnum():
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid config name. Use only letters, numbers, hyphens, and underscores.\n\n"
                        "Please enter a valid config name:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'config_name', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Config name: `{text}`\n\n"
                    "Step 2/6: Trading symbol\n\n"
                    "Examples: `HYPER/USDT:USDT`, `BTC/USDT:USDT`\n\n"
                    "🎯 Enter symbol:"
                )
                return True
            
            # Step 2: Symbol
            elif 'symbol' not in wizard_data:
                if not ValidationUtils.validate_symbol(text):
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid symbol format. Use format like: HYPER/USDT:USDT\n\n"
                        "Please enter a valid symbol:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'symbol', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Symbol: `{text}`\n\n"
                    "Step 3/6: Trading amount (USDT)\n\n"
                    "Examples: `10`, `50`, `100`\n\n"
                    "💰 Enter amount:"
                )
                return True
            
            # Step 3: Amount
            elif 'amount' not in wizard_data:
                if not ValidationUtils.validate_amount(text):
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid amount. Must be a positive number.\n\n"
                        "Please enter a valid amount:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'amount', text)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Amount: `{text} USDT`\n\n"
                    "Step 4/6: Trading direction\n\n"
                    "Options: `long`, `short`, `both`\n\n"
                    "📈 Enter direction:"
                )
                return True
            
            # Step 4: Direction
            elif 'direction' not in wizard_data:
                if text.lower() not in ['long', 'short', 'both']:
                    await self.send_message(
                        context.bot,
                        update.effective_chat.id,
                        "❌ Invalid direction. Must be: long, short, or both\n\n"
                        "Please enter a valid direction:"
                    )
                    return True
                
                self.session_manager.update_wizard_data(user_id, 'direction', text.lower())
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Direction: `{text.lower()}`\n\n"
                    "Step 5/6: Stop loss percentage (optional)\n\n"
                    "Examples: `5`, `10` or 'skip'\n\n"
                    "📉 Stop loss %:"
                )
                return True
            
            # Step 5: Stop loss
            elif 'stop_loss' not in wizard_data:
                if text.lower() == 'skip':
                    stop_loss = 0
                else:
                    if not ValidationUtils.validate_percentage(text):
                        await self.send_message(
                            context.bot,
                            update.effective_chat.id,
                            "❌ Invalid percentage. Must be between 0-100.\n\n"
                            "Enter percentage or 'skip':"
                        )
                        return True
                    stop_loss = float(text)
                
                self.session_manager.update_wizard_data(user_id, 'stop_loss', stop_loss)
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"✅ Stop loss: `{stop_loss}%`\n\n"
                    "Step 6/6: Take profit percentage (optional)\n\n"
                    "Examples: `10`, `20` or 'skip'\n\n"
                    "📈 Take profit %:"
                )
                return True
            
            # Step 6: Take profit & final confirmation
            elif 'take_profit' not in wizard_data:
                if text.lower() == 'skip':
                    take_profit = 0
                else:
                    if not ValidationUtils.validate_percentage(text):
                        await self.send_message(
                            context.bot,
                            update.effective_chat.id,
                            "❌ Invalid percentage. Must be between 0-100.\n\n"
                            "Enter percentage or 'skip':"
                        )
                        return True
                    take_profit = float(text)
                
                self.session_manager.update_wizard_data(user_id, 'take_profit', take_profit)
                
                # Create summary
                summary = self._create_config_summary(wizard_data, take_profit)
                
                keyboard = self.create_keyboard([
                    [("💾 Save Config", "config_save_confirm"), ("❌ Cancel", "config_save_cancel")]
                ])
                
                await self.send_message(
                    context.bot,
                    update.effective_chat.id,
                    f"📋 **Config Summary**\n\n{summary}\n\nReady to save?",
                    reply_markup=keyboard
                )
                return True
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "wizard input")
            )
            self.session_manager.finish_wizard(user_id)
        
        return True
    
    def _create_config_summary(self, wizard_data: Dict, take_profit: float) -> str:
        """Create formatted summary of config"""
        config_name = wizard_data.get('config_name', 'N/A')
        symbol = wizard_data.get('symbol', 'N/A')
        amount = wizard_data.get('amount', 'N/A')
        direction = wizard_data.get('direction', 'N/A')
        stop_loss = wizard_data.get('stop_loss', 0)
        
        summary = f"📁 Name: `{config_name}`\n"
        summary += f"🎯 Symbol: `{symbol}`\n"
        summary += f"💰 Amount: `{amount} USDT`\n"
        summary += f"📈 Direction: `{direction}`\n"
        summary += f"📉 Stop Loss: `{stop_loss}%`\n"
        summary += f"📈 Take Profit: `{take_profit}%`"
        
        return summary
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for config management. Returns True if handled."""
        if not data.startswith('config_'):
            return False
        
        try:
            if data == "config_list":
                await self._handle_callback_list(query)
            elif data == "config_create":
                await self._handle_callback_create(query)
            elif data == "config_edit_select":
                await self._handle_callback_edit_select(query)
            elif data == "config_save_confirm":
                await self._handle_callback_save_confirm(query)
            elif data == "config_save_cancel":
                await self._handle_callback_save_cancel(query)
            elif data.startswith("config_template_"):
                template = data.replace("config_template_", "")
                await self._handle_callback_template(query, template)
            elif data.startswith("config_show_"):
                config = data.replace("config_show_", "")
                await self._handle_callback_show(query, config)
            elif data.startswith("config_edit_"):
                config = data.replace("config_edit_", "")
                await self._handle_callback_edit(query, config)
            elif data.startswith("config_confirm_delete_"):
                config = data.replace("config_confirm_delete_", "")
                await self._handle_callback_confirm_delete(query, config)
            else:
                return False
            
            return True
            
        except Exception as e:
            await query.answer(f"Error: {str(e)}")
            return True
    
    async def _handle_callback_save_confirm(self, query):
        """Handle save config confirmation"""
        user_id = query.from_user.id
        wizard_data = self.session_manager.get_wizard_data(user_id)
        
        if not wizard_data:
            await query.answer("Session expired. Please start again.")
            return
        
        # Build create command
        command = ["./bot.sh", "config", "create", wizard_data['config_name']]
        command.extend([f"symbol={wizard_data['symbol']}"])
        command.extend([f"amount={wizard_data['amount']}"])
        command.extend([f"direction={wizard_data['direction']}"])
        command.extend([f"stop_loss={wizard_data['stop_loss']}"])
        command.extend([f"take_profit={wizard_data['take_profit']}"])
        
        response = await self.execute_botsh_with_response(command)
        
        # Finish wizard
        self.session_manager.finish_wizard(user_id)
        
        keyboard = self.create_keyboard([
            [("📊 Show Config", f"config_show_{wizard_data['config_name']}"), ("📋 List All", "config_list")],
            [("➕ Create Another", "config_create"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(
            query,
            f"💾 **Config Created**\n\n{response}\n\n✅ Config ready to use!",
            reply_markup=keyboard
        )
    
    # Additional callback methods...
    async def _handle_callback_list(self, query):
        """Handle list configs callback"""
        command = ["./bot.sh", "config", "list"]
        response = await self.execute_botsh_with_response(command)
        
        keyboard = self.create_keyboard([
            [("➕ Create New", "config_create"), ("📝 Edit", "config_edit_select")],
            [("🔄 Refresh", "config_list"), ("❌ Close", "close")]
        ])
        
        await self.edit_message(query, f"📁 **Available Config Files**\n\n{response}", reply_markup=keyboard)
    
    # ===============================
    # Abstract Method Implementation
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Main command dispatcher"""
        # This is handled by individual command handlers
        pass 