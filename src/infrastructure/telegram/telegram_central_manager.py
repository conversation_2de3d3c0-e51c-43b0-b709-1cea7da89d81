"""Centralized Telegram Bot Manager - Modular architecture with specialized handlers"""
import asyncio
import logging
import os
import sys
import os

# Fix Docker import path issue (avoid conflict with local docker directory)
venv_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'venv', 'lib', 'python3.13', 'site-packages')
if os.path.exists(venv_path):
    sys.path.insert(0, venv_path)

try:
    import docker
    DOCKER_AVAILABLE = True
except ImportError:
    docker = None
    DOCKER_AVAILABLE = False
from typing import Dict, List, Optional, Any
from datetime import datetime

# Import all specialized modules
from .telegram_base import UserSessionManager
from .telegram_credentials_manager import TelegramCredentialsManager  
from .telegram_bot_manager import TelegramBotManager
from .telegram_config_manager import TelegramConfigManager
from .telegram_wizard_handler import TelegramWizardHandler
from .telegram_notification_manager import TelegramNotificationManager

try:
    from telegram import Bot, InlineKeyboardButton, InlineKeyboardMarkup
    from telegram.ext import Application, CommandHandler, MessageHandler, filters, CallbackQueryHandler
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class CentralTelegramManager:
    """Modular Centralized Telegram Bot Manager"""
    
    def __init__(self):
        self.logger = logging.getLogger('CentralTelegramManager')
        

        
        # Get telegram config from environment
        self.bot_token = os.environ.get('TELEGRAM_BOT_TOKEN')
        self.chat_id = os.environ.get('TELEGRAM_CHAT_ID')
        
        if not self.bot_token or not self.chat_id:
            raise ValueError(
                "🔑 Telegram Configuration Missing\n\n"
                "Please set environment variables:\n"
                "• TELEGRAM_BOT_TOKEN='your_bot_token'\n"
                "• TELEGRAM_CHAT_ID='your_chat_id'\n\n"
                "📖 See TELEGRAM_SETUP.md for detailed setup instructions."
            )
        
        # Docker client for container management
        try:
            if not DOCKER_AVAILABLE or docker is None:
                self.logger.warning("Docker SDK not available - container monitoring disabled")
                self.docker_client = None
            else:
                self.docker_client = docker.from_env()
                self.logger.info(f"✅ Docker client connected - Version: {docker.__version__}")
        except Exception as e:
            self.logger.error(f"Failed to connect to Docker: {e}")
            self.docker_client = None
        
        self.application: Optional[Application] = None
        self.bot: Optional[Bot] = None
        self.is_running = False
        
        # Initialize session manager
        self.session_manager = UserSessionManager()
        
        # Initialize specialized modules
        self.credentials_manager = TelegramCredentialsManager(self.session_manager)
        self.bot_manager = TelegramBotManager(self.session_manager)
        self.config_manager = TelegramConfigManager(self.session_manager)
        self.wizard_handler = TelegramWizardHandler(self.session_manager)
        self.notification_manager = TelegramNotificationManager(self.session_manager)
        
        # List of all handlers for routing
        self.handlers = [
            self.credentials_manager,
            self.bot_manager,
            self.config_manager,
            self.notification_manager
        ]
        
        # Cache for active containers
        self.active_containers: Dict[str, Dict] = {}
    
    async def start(self):
        """Start central telegram bot"""
        try:
            self.logger.info("🤖 Starting Modular Central Telegram Bot...")
            
            # Create application
            if not self.bot_token:
                raise ValueError("Bot token is required")
            self.application = Application.builder().token(self.bot_token).build()
            self.bot = self.application.bot
            
            # Add command handlers
            self._setup_handlers()
            
            # Start container monitoring
            asyncio.create_task(self._monitor_containers())
            
            # Start session cleanup
            asyncio.create_task(self._periodic_cleanup())
            
            self.is_running = True
            self.start_time = datetime.now()
            self.logger.info("✅ Modular Central Telegram Bot starting...")
            
            # Send startup message with module status
            startup_msg = (
                "🎛️ **Central Trading Bot Manager Started**\n\n"
                "✅ **Active Modules:**\n"
                "• 🔑 Credentials Manager\n"
                "• 🤖 Bot Manager\n"
                "• ⚙️ Config Manager\n"
                "• 🧙‍♂️ Wizard Handler\n"
                "• 🔔 Notification Manager\n\n"
                "💡 Type `/help` to get started!"
            )
            # Send message after bot starts
            asyncio.create_task(self._send_startup_message(startup_msg))
            
            # Initialize and start application
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling(drop_pending_updates=True)
            
            # Keep running
            await self.application.updater.idle()
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start Central Telegram Bot: {e}")
            raise
    
    async def stop(self):
        """Stop central telegram bot"""
        try:
            await self._send_message("🛑 **Central Trading Bot Manager Stopped**")
            
            if self.application:
                await self.application.stop()
                await self.application.shutdown()
            
            self.is_running = False
            self.logger.info("✅ Central Telegram Bot stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping Central Telegram Bot: {e}")
    
    def _setup_handlers(self):
        """Setup all command handlers using modular approach"""
        if not self.application:
            raise RuntimeError("Application not initialized")
        
        # Core system commands
        self.application.add_handler(CommandHandler("start", self._start_command))
        self.application.add_handler(CommandHandler("help", self._help_command))
        self.application.add_handler(CommandHandler("status", self._system_status_command))
        
        # Credential Management Commands
        self.application.add_handler(CommandHandler("setkey", self.credentials_manager.handle_setkey_command))
        self.application.add_handler(CommandHandler("addcreds", self.credentials_manager.handle_addcreds_wizard))
        self.application.add_handler(CommandHandler("listcreds", self.credentials_manager.handle_listcreds_command))
        self.application.add_handler(CommandHandler("selectcreds", self.credentials_manager.handle_selectcreds_command))
        self.application.add_handler(CommandHandler("showcreds", self.credentials_manager.handle_showcreds_command))
        self.application.add_handler(CommandHandler("deletecreds", self.credentials_manager.handle_deletecreds_command))
        
        # Bot Management Commands
        self.application.add_handler(CommandHandler("list", self.bot_manager.handle_list_command))
        self.application.add_handler(CommandHandler("startbot", self.bot_manager.handle_startbot_command))
        self.application.add_handler(CommandHandler("createbot", self.bot_manager.handle_createbot_wizard))
        self.application.add_handler(CommandHandler("logs", self.bot_manager.handle_logs_command))
        self.application.add_handler(CommandHandler("stop", self.bot_manager.handle_stop_command))
        self.application.add_handler(CommandHandler("restart", self.bot_manager.handle_restart_command))
        self.application.add_handler(CommandHandler("stopall", self.bot_manager.handle_stopall_command))
        
        # Config Management Commands
        self.application.add_handler(CommandHandler("listconfigs", self.config_manager.handle_listconfigs_command))
        self.application.add_handler(CommandHandler("showconfig", self.config_manager.handle_showconfig_command))
        self.application.add_handler(CommandHandler("createconfig", self.config_manager.handle_createconfig_wizard))
        self.application.add_handler(CommandHandler("editconfig", self.config_manager.handle_editconfig_command))
        self.application.add_handler(CommandHandler("deleteconfig", self.config_manager.handle_deleteconfig_command))
        self.application.add_handler(CommandHandler("templates", self.config_manager.handle_templates_command))
        self.application.add_handler(CommandHandler("template", self.config_manager.handle_template_command))
        
        # Notification Management Commands
        self.application.add_handler(CommandHandler("subscribe", self.notification_manager.handle_subscribe_command))
        self.application.add_handler(CommandHandler("unsubscribe", self.notification_manager.handle_unsubscribe_command))
        self.application.add_handler(CommandHandler("alerts", self.notification_manager.handle_alerts_command))
        
        # Wizard Management Commands
        self.application.add_handler(CommandHandler("cancel", self.wizard_handler.handle_cancel_command))
        self.application.add_handler(CommandHandler("wizardstatus", self.wizard_handler.handle_status_wizard_command))
        
        # Text message handler for wizards
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_text_input))
        
        # Callback query handler for all interactive elements
        self.application.add_handler(CallbackQueryHandler(self._handle_callback))
    
    async def _handle_text_input(self, update, context):
        """Route text input to appropriate wizard handler"""
        try:
            user_id = update.effective_user.id
            
            # Check if user has active wizard
            if not self.session_manager.is_wizard_active(user_id):
                # No active wizard, ignore text input
                return
            
            # Route to wizard handler
            await self.wizard_handler.handle_wizard_text_input(update, context, self.handlers)
            
        except Exception as e:
            self.logger.error(f"Error handling text input: {e}")
            await self._send_error_message(update.effective_chat.id, str(e))
    
    async def _handle_callback(self, update, context):
        """Route callback queries to appropriate handlers"""
        try:
            query = update.callback_query
            await query.answer()  # Acknowledge the callback
            
            data = query.data
            
            # Global close button
            if data == "close":
                await query.edit_message_text("✅ Closed")
                return
            
            # Route to appropriate handler
            for handler in self.handlers + [self.wizard_handler]:
                if await handler.handle_callback(query, data):
                    return
            
            # Unhandled callback
            self.logger.warning(f"Unhandled callback: {data}")
            await query.edit_message_text(f"❓ Unknown action: {data}")
            
        except Exception as e:
            self.logger.error(f"Error handling callback: {e}")
            await query.answer(f"Error: {str(e)}")
    
    async def _start_command(self, update, context):
        """Handle /start command"""
        try:
            welcome_msg = (
                "🎛️ **Welcome to Central Trading Bot Manager**\n\n"
                "🚀 **Get Started:**\n"
                "1. Setup credentials: `/addcreds`\n"
                "2. Create trading bot: `/createbot`\n"
                "3. Monitor performance: `/list`\n\n"
                "📋 **Quick Commands:**\n"
                "• `/help` - Show all commands\n"
                "• `/status` - System status\n"
                "• `/subscribe` - Enable notifications\n\n"
                "💡 Use interactive buttons for easier navigation!"
            )
            
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔑 Add Credentials", callback_data="creds_add"),
                 InlineKeyboardButton("🤖 Create Bot", callback_data="bot_create")],
                [InlineKeyboardButton("📋 List Bots", callback_data="bot_list"),
                 InlineKeyboardButton("📊 System Status", callback_data="system_status")],
                [InlineKeyboardButton("📖 Help", callback_data="help"),
                 InlineKeyboardButton("🔔 Notifications", callback_data="notify_configure")]
            ])
            
            await update.message.reply_text(
                welcome_msg,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=keyboard
            )
            
        except Exception as e:
            self.logger.error(f"Error in start command: {e}")
            await self._send_error_message(update.effective_chat.id, str(e))
    
    async def _help_command(self, update, context):
        """Handle /help command"""
        try:
            help_msg = (
                "📖 **Central Trading Bot Manager - Help**\n\n"
                "🔑 **Credential Management:**\n"
                "• <code>/setkey &lt;api_key&gt; &lt;secret&gt;</code> - Quick setup\n"
                "• `/addcreds` - Advanced credential wizard\n"
                "• `/listcreds` - List all credentials\n"
                "• `/showcreds [profile]` - Show credential details\n"
                "• <code>/deletecreds &lt;profile&gt;</code> - Delete credentials\n\n"
                
                "🤖 **Bot Management:**\n"
                "• `/createbot` - Step-by-step bot creation\n"
                "• `/startbot <symbol> <amount>` - Quick start\n"
                "• `/list` - List all containers\n"
                "• `/stop <container>` - Stop specific bot\n"
                "• `/restart <container>` - Restart bot\n"
                "• `/logs <container>` - View bot logs\n"
                "• `/stopall` - Stop all bots\n\n"
                
                "⚙️ **Config Management:**\n"
                "• `/createconfig` - Create config wizard\n"
                "• `/listconfigs` - List configurations\n"
                "• `/editconfig <config> <param> <value>` - Edit\n"
                "• `/templates` - Available templates\n"
                "• `/template <type> [name]` - Create from template\n\n"
                
                "🔔 **Notifications:**\n"
                "• `/subscribe` - Enable notifications\n"
                "• `/alerts` - Configure alert thresholds\n"
                "• `/unsubscribe` - Disable notifications\n\n"
                
                "🧙‍♂️ **Wizard Management:**\n"
                "• `/cancel` - Cancel active wizard\n"
                "• `/wizardstatus` - Show wizard progress\n\n"
                
                "🔧 **System:**\n"
                "• `/status` - System status & statistics\n"
                "• `/start` - Show welcome message\n"
                "• `/help` - This help message\n\n"
                
                "💡 **Tips:**\n"
                "• Use interactive buttons for easier navigation\n"
                "• Wizards guide you through complex operations\n"
                "• Subscribe to notifications for real-time updates\n"
                "• All operations use secure credential storage"
            )
            
            await update.message.reply_text(help_msg, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            self.logger.error(f"Error in help command: {e}")
            await self._send_error_message(update.effective_chat.id, str(e))
    
    async def _system_status_command(self, update, context):
        """Handle /status command - Show system statistics"""
        try:
            # Get statistics from all modules
            wizard_stats = self.wizard_handler.get_wizard_statistics()
            notification_stats = self.notification_manager.get_notification_stats()
            
            # Get container count
            container_count = len(self.active_containers)
            
            status_msg = (
                "📊 **System Status**\n\n"
                f"🐳 **Containers:** {container_count} active\n"
                f"👥 **Sessions:** {wizard_stats['total_sessions']}\n"
                f"🧙‍♂️ **Active Wizards:** {self.wizard_handler.get_active_wizards_count()}\n"
                f"🔔 **Subscribers:** {notification_stats['total_subscribers']}\n\n"
                
                "📈 **Module Status:**\n"
                "• Credentials Manager: ✅ Active\n"
                "• Bot Manager: ✅ Active\n"
                "• Config Manager: ✅ Active\n"
                "• Wizard Handler: ✅ Active\n"
                "• Notification Manager: ✅ Active\n\n"
                
                f"🕐 **Uptime:** {self._get_uptime()}\n"
                f"💾 **Memory:** Docker containers monitored\n"
                f"📡 **Connection:** Telegram API active"
            )
            
            if wizard_stats.get('active_wizards'):
                status_msg += f"\n\n🧙‍♂️ **Active Wizard Types:**\n"
                for wizard_type, count in wizard_stats['active_wizards'].items():
                    status_msg += f"• {wizard_type}: {count}\n"
            
            keyboard = InlineKeyboardMarkup([
                [InlineKeyboardButton("🔄 Refresh", callback_data="system_status"),
                 InlineKeyboardButton("📋 List Bots", callback_data="bot_list")],
                [InlineKeyboardButton("🔔 Notifications", callback_data="notify_status"),
                 InlineKeyboardButton("❌ Close", callback_data="close")]
            ])
            
            await update.message.reply_text(
                status_msg,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=keyboard
            )
            
        except Exception as e:
            self.logger.error(f"Error in status command: {e}")
            await self._send_error_message(update.effective_chat.id, str(e))
    
    def _get_uptime(self) -> str:
        """Get system uptime"""
        # Simple uptime since bot start
        return "Active (since restart)"
    
    async def _send_startup_message(self, message: str):
        """Send startup message after a short delay"""
        await asyncio.sleep(2)  # Wait for bot to be fully ready
        await self._send_message(message)
    
    async def _monitor_containers(self):
        """Monitor docker containers and update cache"""
        first_warning = True
        
        while self.is_running:
            try:
                # Skip monitoring if docker client not available
                if not DOCKER_AVAILABLE or self.docker_client is None:
                    if first_warning:
                        self.logger.warning("Docker SDK not available - container monitoring disabled")
                        first_warning = False
                    await asyncio.sleep(60)
                    continue
                
                # Update container cache
                containers = self.docker_client.containers.list(all=True)
                
                self.active_containers = {}
                container_count = 0
                
                for container in containers:
                    try:
                        container_info = {
                            'id': container.id,
                            'status': container.status,
                            'image': container.image.tags[0] if container.image.tags else 'unknown',
                            'created': container.attrs.get('Created', ''),
                            'symbol': self._extract_symbol(container)
                        }
                        
                        # Store all containers, not just trading ones
                        self.active_containers[container.name] = container_info
                        container_count += 1
                        
                    except Exception as e:
                        self.logger.warning(f"Error processing container {container.id}: {e}")
                
                if container_count > 0:
                    self.logger.debug(f"Monitoring {container_count} containers")
                
                # Sleep for 30 seconds before next check
                await asyncio.sleep(30)
                
            except Exception as e:
                self.logger.error(f"Error monitoring containers: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of expired sessions"""
        while self.is_running:
            try:
                await self.wizard_handler.cleanup_expired_sessions()
                await asyncio.sleep(3600)  # Cleanup every hour
            except Exception as e:
                self.logger.error(f"Error in periodic cleanup: {e}")
                await asyncio.sleep(3600)
    
    def _is_trading_container(self, container) -> bool:
        """Check if container is a trading bot"""
        name = container.name.lower()
        return 'trader' in name or 'bot' in name or 'trading' in name
    
    def _extract_symbol(self, container) -> str:
        """Extract trading symbol from container name or environment"""
        try:
            # Try to get from environment variables
            env_vars = container.attrs.get('Config', {}).get('Env', [])
            for env in env_vars:
                if env.startswith('SYMBOL='):
                    return env.split('=', 1)[1]
            
            # Fallback to container name parsing
            name = container.name
            if '_' in name:
                parts = name.split('_')
                for part in parts:
                    if '/' in part or part.upper() in ['BTC', 'ETH', 'HYPER']:
                        return part.upper()
            
            return 'Unknown'
            
        except Exception:
            return 'Unknown'
    
    def get_container_status(self) -> Dict[str, Any]:
        """Get status of all Docker containers for Telegram display"""
        if not DOCKER_AVAILABLE or self.docker_client is None:
            return {
                'error': 'Docker SDK not available',
                'suggestion': 'Install Docker SDK: pip install docker==7.1.0',
                'total': 0,
                'running': 0,
                'stopped': 0,
                'containers': []
            }
        
        try:
            containers = self.docker_client.containers.list(all=True)
            running_containers = [c for c in containers if c.status == 'running']
            
            result = {
                'total': len(containers),
                'running': len(running_containers),
                'stopped': len(containers) - len(running_containers),
                'docker_version': docker.__version__ if docker else 'Unknown',
                'containers': []
            }
            
            for c in containers:
                try:
                    # Get creation time
                    created = c.attrs.get('Created', '')[:19] if c.attrs.get('Created') else 'Unknown'
                    
                    container_info = {
                        'name': c.name,
                        'status': c.status,
                        'id': c.id[:12],
                        'image': c.image.tags[0] if c.image.tags else 'Unknown',
                        'created': created,
                        'symbol': self._extract_symbol(c)
                    }
                    result['containers'].append(container_info)
                    
                except Exception as e:
                    self.logger.warning(f"Error getting info for container {c.id}: {e}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"Docker operation failed: {e}")
            return {
                'error': f'Failed to get container status: {e}',
                'total': 0,
                'running': 0,
                'stopped': 0,
                'containers': []
            }
    
    def start_container(self, container_name: str) -> Dict[str, Any]:
        """Start a Docker container"""
        if not DOCKER_AVAILABLE or self.docker_client is None:
            return {'error': 'Docker SDK not available'}
        
        try:
            container = self.docker_client.containers.get(container_name)
            container.start()
            
            return {
                'success': True,
                'message': f'Container {container_name} started successfully',
                'status': container.status
            }
            
        except Exception as e:
            self.logger.error(f"Failed to start container {container_name}: {e}")
            return {'error': f'Failed to start container: {e}'}
    
    def stop_container(self, container_name: str) -> Dict[str, Any]:
        """Stop a Docker container"""
        if not DOCKER_AVAILABLE or self.docker_client is None:
            return {'error': 'Docker SDK not available'}
        
        try:
            container = self.docker_client.containers.get(container_name)
            container.stop()
            
            return {
                'success': True,
                'message': f'Container {container_name} stopped successfully',
                'status': container.status
            }
            
        except Exception as e:
            self.logger.error(f"Failed to stop container {container_name}: {e}")
            return {'error': f'Failed to stop container: {e}'}
    
    def get_container_logs(self, container_name: str, lines: int = 50) -> Dict[str, Any]:
        """Get logs from a Docker container"""
        if not DOCKER_AVAILABLE or self.docker_client is None:
            return {'error': 'Docker SDK not available'}
        
        try:
            container = self.docker_client.containers.get(container_name)
            logs = container.logs(tail=lines).decode('utf-8', errors='ignore')
            
            return {
                'success': True,
                'container': container_name,
                'logs': logs,
                'lines': len(logs.split('\n')) if logs else 0
            }
            
        except Exception as e:
            self.logger.error(f"Failed to get logs for container {container_name}: {e}")
            return {'error': f'Failed to get container logs: {e}'}
    
    async def _send_message(self, message: str):
        """Send message to configured chat"""
        if not self.bot or not self.chat_id:
            raise RuntimeError("Bot or chat_id not configured")
        try:
            await self.bot.send_message(
                chat_id=self.chat_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
    
    async def _send_error_message(self, chat_id: int, error: str):
        """Send error message to specific chat"""
        if not self.bot:
            raise RuntimeError("Bot not configured")
        try:
            await self.bot.send_message(
                chat_id=chat_id,
                text=f"❌ **Error**\n\n{error}",
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Failed to send error message: {e}")


async def main():
    """Main function to run Central Telegram Manager"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    manager = CentralTelegramManager()
    
    try:
        await manager.start()
        
        # Keep running
        while manager.is_running:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logging.info("Shutting down...")
    except Exception as e:
        logging.error(f"Error in main: {e}")
    finally:
        await manager.stop()


if __name__ == "__main__":
    asyncio.run(main()) 