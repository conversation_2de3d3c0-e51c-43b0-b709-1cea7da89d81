"""Telegram Notification Manager - Handle real-time notifications and alerts"""
from typing import Dict, List, Optional, Any
from datetime import datetime
from .telegram_base import Telegram<PERSON><PERSON><PERSON><PERSON><PERSON>, MessageFormatter

try:
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TelegramNotificationManager(TelegramBaseHandler):
    """Manages notifications, alerts, and real-time updates"""
    
    def __init__(self, session_manager):
        super().__init__('TelegramNotificationManager')
        self.session_manager = session_manager
        self.subscribers = {}  # chat_id: subscription_preferences
        self.alert_thresholds = {}  # chat_id: alert_settings
    
    # ===============================
    # Subscription Management
    # ===============================
    
    async def handle_subscribe_command(self, update, context):
        """Handle /subscribe command - Subscribe to notifications"""
        try:
            chat_id = update.effective_chat.id
            
            # Default subscription preferences
            self.subscribers[chat_id] = {
                'trade_alerts': True,
                'bot_status': True,
                'error_alerts': True,
                'profit_loss': True,
                'position_updates': False,
                'created_at': datetime.now().isoformat()
            }
            
            keyboard = self.create_keyboard([
                [("⚙️ Configure", "notify_configure"), ("📊 Status", "notify_status")],
                [("🔕 Unsubscribe", "notify_unsubscribe"), ("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                "🔔 <b>Notification Subscription</b>\n\n"
                "✅ Successfully subscribed to notifications!\n\n"
                "<b>Default Settings:</b>\n"
                "• Trade alerts: Enabled\n"
                "• Bot status: Enabled\n"
                "• Error alerts: Enabled\n"
                "• Profit/Loss: Enabled\n"
                "• Position updates: Disabled\n\n"
                "💡 Use buttons below to customize.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "subscribe")
            )
    
    async def handle_unsubscribe_command(self, update, context):
        """Handle /unsubscribe command - Unsubscribe from notifications"""
        try:
            chat_id = update.effective_chat.id
            
            if chat_id not in self.subscribers:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "ℹ️ You are not currently subscribed to notifications.\n\n"
                    "Use `/subscribe` to enable notifications."
                )
                return
            
            # Confirmation keyboard
            keyboard = self.create_keyboard([
                [("✅ Yes, Unsubscribe", "notify_confirm_unsubscribe"), ("❌ Cancel", "close")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                "🔕 <b>Confirm Unsubscribe</b>\n\n"
                "Are you sure you want to unsubscribe from all notifications?\n\n"
                "You can re-subscribe anytime with `/subscribe`.",
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "unsubscribe")
            )
    
    async def handle_alerts_command(self, update, context):
        """Handle /alerts command - Configure alert thresholds"""
        try:
            chat_id = update.effective_chat.id
            
            if chat_id not in self.subscribers:
                await self.send_message(
                    context.bot,
                    chat_id,
                    "❌ You need to subscribe first.\n\n"
                    "Use `/subscribe` to enable notifications."
                )
                return
            
            current_alerts = self.alert_thresholds.get(chat_id, {})
            
            alert_msg = "🚨 <b>Alert Configuration</b>\n\n"
            alert_msg += "<b>Current Thresholds:</b>\n"
            alert_msg += f"• Profit alert: {current_alerts.get('profit_threshold', 'Not set')}%\n"
            alert_msg += f"• Loss alert: {current_alerts.get('loss_threshold', 'Not set')}%\n"
            alert_msg += f"• Position size: {current_alerts.get('position_threshold', 'Not set')} USDT\n\n"
            alert_msg += "<b>Quick Setup:</b>\n"
            alert_msg += "• `/alerts profit 10` - Alert on 10% profit\n"
            alert_msg += "• `/alerts loss 5` - Alert on 5% loss\n"
            alert_msg += "• `/alerts position 1000` - Alert on 1000 USDT position"
            
            keyboard = self.create_keyboard([
                [("💰 Set Profit", "alert_profit"), ("📉 Set Loss", "alert_loss")],
                [("📊 Position Size", "alert_position"), ("🔄 Reset All", "alert_reset")],
                [("❌ Close", "close")]
            ])
            
            await self.send_message(
                context.bot,
                chat_id,
                alert_msg,
                reply_markup=keyboard
            )
            
        except Exception as e:
            await self.send_message(
                context.bot,
                update.effective_chat.id,
                self.format_error_message(str(e), "alerts")
            )
    
    # ===============================
    # Notification Broadcasting
    # ===============================
    
    async def broadcast_trade_alert(self, bot, trade_data: Dict):
        """Broadcast trade execution alert to subscribers"""
        message = self._format_trade_alert(trade_data)
        
        for chat_id, preferences in self.subscribers.items():
            if preferences.get('trade_alerts', False):
                try:
                    await bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        parse_mode=ParseMode.MARKDOWN
                    )
                except Exception as e:
                    self.logger.error(f"Failed to send trade alert to {chat_id}: {e}")
    
    async def broadcast_bot_status(self, bot, status_data: Dict):
        """Broadcast bot status changes to subscribers"""
        message = self._format_status_alert(status_data)
        
        for chat_id, preferences in self.subscribers.items():
            if preferences.get('bot_status', False):
                try:
                    await bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        parse_mode=ParseMode.MARKDOWN
                    )
                except Exception as e:
                    self.logger.error(f"Failed to send status alert to {chat_id}: {e}")
    
    async def broadcast_error_alert(self, bot, error_data: Dict):
        """Broadcast error alerts to subscribers"""
        message = self._format_error_alert(error_data)
        
        for chat_id, preferences in self.subscribers.items():
            if preferences.get('error_alerts', False):
                try:
                    await bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        parse_mode=ParseMode.MARKDOWN
                    )
                except Exception as e:
                    self.logger.error(f"Failed to send error alert to {chat_id}: {e}")
    
    async def check_and_send_threshold_alerts(self, bot, performance_data: Dict):
        """Check performance against thresholds and send alerts"""
        for chat_id, thresholds in self.alert_thresholds.items():
            if chat_id not in self.subscribers:
                continue
            
            # Check profit threshold
            profit_pct = performance_data.get('profit_percentage', 0)
            profit_threshold = thresholds.get('profit_threshold')
            
            if profit_threshold and profit_pct >= profit_threshold:
                message = f"🎉 <b>Profit Alert</b>\n\n" \
                         f"Your bot reached {profit_pct:.2f}% profit!\n" \
                         f"Target was: {profit_threshold}%"
                
                try:
                    await bot.send_message(chat_id=chat_id, text=message, parse_mode=ParseMode.MARKDOWN)
                except Exception as e:
                    self.logger.error(f"Failed to send profit alert to {chat_id}: {e}")
            
            # Check loss threshold
            if profit_pct < 0:
                loss_threshold = thresholds.get('loss_threshold')
                if loss_threshold and abs(profit_pct) >= loss_threshold:
                    message = f"⚠️ <b>Loss Alert</b>\n\n" \
                             f"Your bot has {abs(profit_pct):.2f}% loss!\n" \
                             f"Alert threshold: {loss_threshold}%"
                    
                    try:
                        await bot.send_message(chat_id=chat_id, text=message, parse_mode=ParseMode.MARKDOWN)
                    except Exception as e:
                        self.logger.error(f"Failed to send loss alert to {chat_id}: {e}")
    
    # ===============================
    # Message Formatters
    # ===============================
    
    def _format_trade_alert(self, trade_data: Dict) -> str:
        """Format trade execution alert message"""
        symbol = trade_data.get('symbol', 'Unknown')
        side = trade_data.get('side', 'Unknown')
        amount = trade_data.get('amount', 0)
        price = trade_data.get('price', 0)
        timestamp = trade_data.get('timestamp', datetime.now().strftime('%H:%M:%S'))
        
        emoji = "🟢" if side.lower() == 'buy' else "🔴"
        
        message = f"{emoji} <b>Trade Executed</b>\n\n"
        message += f"Symbol: `{symbol}`\n"
        message += f"Side: `{side.upper()}`\n"
        message += f"Amount: `{amount} USDT`\n"
        message += f"Price: `{price}`\n"
        message += f"Time: `{timestamp}`"
        
        return message
    
    def _format_status_alert(self, status_data: Dict) -> str:
        """Format bot status alert message"""
        bot_name = status_data.get('bot_name', 'Trading Bot')
        status = status_data.get('status', 'Unknown')
        message_text = status_data.get('message', '')
        
        emoji = MessageFormatter.status_emoji(status)
        
        message = f"{emoji} <b>Bot Status Update</b>\n\n"
        message += f"Bot: `{bot_name}`\n"
        message += f"Status: `{status.upper()}`\n"
        
        if message_text:
            message += f"Message: {message_text}\n"
        
        message += f"Time: `{datetime.now().strftime('%H:%M:%S')}`"
        
        return message
    
    def _format_error_alert(self, error_data: Dict) -> str:
        """Format error alert message"""
        error_type = error_data.get('type', 'Unknown Error')
        error_message = error_data.get('message', 'No details available')
        bot_name = error_data.get('bot_name', 'Trading Bot')
        
        message = f"🚨 <b>Error Alert</b>\n\n"
        message += f"Bot: `{bot_name}`\n"
        message += f"Type: `{error_type}`\n"
        message += f"Message: {error_message[:200]}...\n" if len(error_message) > 200 else f"Message: {error_message}\n"
        message += f"Time: `{datetime.now().strftime('%H:%M:%S')}`"
        
        return message
    
    # ===============================
    # Callback Handlers
    # ===============================
    
    async def handle_callback(self, query, data: str) -> bool:
        """Handle callback queries for notifications. Returns True if handled."""
        if not data.startswith('notify_') and not data.startswith('alert_'):
            return False
        
        try:
            if data == "notify_configure":
                await self._handle_callback_configure(query)
            elif data == "notify_status":
                await self._handle_callback_status(query)
            elif data == "notify_unsubscribe":
                await self._handle_callback_unsubscribe(query)
            elif data == "notify_confirm_unsubscribe":
                await self._handle_callback_confirm_unsubscribe(query)
            elif data == "alert_profit":
                await self._handle_callback_alert_profit(query)
            elif data == "alert_loss":
                await self._handle_callback_alert_loss(query)
            elif data == "alert_position":
                await self._handle_callback_alert_position(query)
            elif data == "alert_reset":
                await self._handle_callback_alert_reset(query)
            else:
                return False
            
            return True
            
        except Exception as e:
            await query.answer(f"Error: {str(e)}")
            return True
    
    async def _handle_callback_configure(self, query):
        """Handle notification configuration callback"""
        chat_id = query.message.chat_id
        preferences = self.subscribers.get(chat_id, {})
        
        config_msg = "⚙️ <b>Notification Settings</b>\n\n"
        config_msg += f"Trade alerts: {'✅' if preferences.get('trade_alerts') else '❌'}\n"
        config_msg += f"Bot status: {'✅' if preferences.get('bot_status') else '❌'}\n"
        config_msg += f"Error alerts: {'✅' if preferences.get('error_alerts') else '❌'}\n"
        config_msg += f"Profit/Loss: {'✅' if preferences.get('profit_loss') else '❌'}\n"
        config_msg += f"Position updates: {'✅' if preferences.get('position_updates') else '❌'}\n\n"
        config_msg += "💡 Use `/subscribe` command with parameters to change settings."
        
        await self.edit_message(query, config_msg)
    
    async def _handle_callback_confirm_unsubscribe(self, query):
        """Handle confirm unsubscribe callback"""
        chat_id = query.message.chat_id
        
        if chat_id in self.subscribers:
            del self.subscribers[chat_id]
        
        if chat_id in self.alert_thresholds:
            del self.alert_thresholds[chat_id]
        
        await self.edit_message(
            query,
            "🔕 <b>Unsubscribed Successfully</b>\n\n"
            "You will no longer receive notifications.\n\n"
            "Use `/subscribe` to re-enable notifications anytime."
        )
    
    # ===============================
    # Utility Functions
    # ===============================
    
    def is_subscribed(self, chat_id: int) -> bool:
        """Check if chat is subscribed to notifications"""
        return chat_id in self.subscribers
    
    def get_subscription_count(self) -> int:
        """Get total number of subscribers"""
        return len(self.subscribers)
    
    def get_notification_stats(self) -> Dict[str, Any]:
        """Get notification system statistics"""
        return {
            'total_subscribers': len(self.subscribers),
            'alert_configs': len(self.alert_thresholds),
            'subscription_breakdown': {
                'trade_alerts': sum(1 for p in self.subscribers.values() if p.get('trade_alerts')),
                'bot_status': sum(1 for p in self.subscribers.values() if p.get('bot_status')),
                'error_alerts': sum(1 for p in self.subscribers.values() if p.get('error_alerts')),
                'profit_loss': sum(1 for p in self.subscribers.values() if p.get('profit_loss')),
                'position_updates': sum(1 for p in self.subscribers.values() if p.get('position_updates'))
            }
        }
    
    # ===============================
    # Abstract Method Implementation
    # ===============================
    
    async def handle_command(self, update, context) -> None:
        """Main command dispatcher"""
        # This is handled by individual command handlers
        pass 