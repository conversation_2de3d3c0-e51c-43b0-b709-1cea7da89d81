# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a professional cryptocurrency trading bot written in Python that implements a DCA (Dollar Cost Averaging) strategy with technical analysis. The bot uses clean architecture principles with clear separation of concerns across layers.

## Development Commands

### Running the Bot
```bash
# Quick launcher script (recommended)
./run.sh help                     # Show all available commands
./run.sh dashboard               # Launch CLI dashboard
./run.sh start                   # Start live trading
./run.sh test                    # Run in safe test mode
./run.sh config                  # Show configuration details

# With debug logging
./run.sh dashboard --debug       # CLI dashboard with debug logs
./run.sh start --debug          # Live trading with debug logs
./run.sh test --debug           # Test mode with debug logs

# Direct Python commands
python main.py                   # Show usage guide
python main.py --dashboard       # Interactive CLI dashboard
python main.py --start           # Start trading bot
python main.py --test            # Safe testing mode
python main.py --show-config     # Display configuration
python main.py --start --debug   # Start with debug logging
```

### Dependencies and Setup
```bash
# Install Python dependencies
pip install -r requirements.txt

# Set up environment variables
export BYBIT_API_KEY="your_api_key"
export BYBIT_API_SECRET="your_api_secret"
export TEST_MODE="true"  # For testing

# Test components
python test_components.py

# Clear logs
./clear_logs.sh
```

### Testing
- Always test with `--test` flag before live trading
- Component tests: `python test_components.py`
- The bot has comprehensive test mode that simulates trading without real money

## Architecture

The project follows Clean Architecture with four main layers:

### Core Layer (`src/core/`)
- **models/**: Domain entities (Position, Order, Signal, TradeConfig, Candle)
- **strategies/**: Trading strategies (DCAStrategy with technical analysis)
- **risk/**: Risk management logic (RiskManager)

### Infrastructure Layer (`src/infrastructure/`)
- **exchange/**: Exchange connectors (Bybit integration via CCXT)
- **data/**: Data persistence (StateManager, StatisticsTracker)
- **config/**: Configuration management (ConfigManager)
- **logging/**: Logging system (TradeLogger)

### Application Layer (`src/application/`)
- **engine/**: Main trading engine (TradingEngine)
- **events/**: Event bus system for component communication

### Presentation Layer (`src/presentation/`)
- **cli/**: Command-line interface and dashboard
- **monitoring/**: Monitoring interfaces

## Key Components

### TradingEngine (`src/application/engine/trading_engine.py`)
- Main orchestrator that coordinates all components
- Handles bot lifecycle, signal processing, and order execution
- Manages state persistence and error recovery

### DCAStrategy (`src/core/strategies/dca_strategy.py`)
- Implements Dollar Cost Averaging with technical indicators
- Uses RSI, EMA, Bollinger Bands, MACD for signal generation
- Supports multiple timeframes (15m, 1h, 4h) and DCA levels

### Configuration (`config.json`)
Key configuration sections:
- **symbol**: Trading pair (e.g., "SAHARA/USDT")
- **direction**: Trading direction ("LONG", "SHORT", "AUTO")
- **amount**: Base amount per order
- **indicators**: Technical analysis settings with thresholds
- **dca**: DCA strategy configuration with multiple levels
- **risk**: Risk management parameters including position sizing

## Data Management

### CSV Files (`data/`)
- `positions.csv`: Position history and state
- `daily_stats.csv`: Daily performance metrics
- `dca_orders_state.csv`: DCA order tracking
- `trades.csv`: Trade execution history
- `state_snapshot.json`: Current system state

### Log Files (`logs/`)
- `tradingengine.log`: Main engine operations
- `dcastrategy.log`: Strategy-specific logs
- `errors.log`: Error tracking
- `trades.jsonl`: Trade execution logs in JSON format

## Important Configuration Notes

### Signal Thresholds
- **Test Mode**: Use low thresholds (0.15) for testing strategies
- **Production**: Use higher thresholds (0.6+) for reliable signals
- Thresholds control minimum confidence level for trade signals

### Risk Management
- **Capital Management**: Bot auto-updates balance from exchange every 5 minutes
- **Position Sizing**: Dynamic calculation based on current capital and risk percentage
- **Stop Loss/Take Profit**: Configurable with trailing stop options

### DCA Strategy
- **Multiple Levels**: Supports up to 3 DCA orders with volume scaling
- **Trigger Conditions**: BB_LOWER (Bollinger Lower Band) and EMA_89 strategies
- **Price Deviation**: Configurable percentage deviation for DCA triggers

## Development Guidelines

### Code Style
- Uses Python 3.8+ with async/await patterns
- Type hints throughout the codebase
- Dataclasses for structured data (models)
- Clean separation of concerns

### Error Handling
- Comprehensive exception handling in all components
- Graceful shutdown with signal handlers (SIGINT, SIGTERM)
- State recovery on restart
- Detailed logging for debugging

### Testing Approach
- Test mode that simulates all trading operations
- Component testing with `test_components.py`
- Always test configuration changes before live trading

## Safety Features

### Risk Controls
- Daily loss limits and trade count limits
- Position size limits based on capital percentage
- Cooldown periods after losses
- Maximum concurrent positions limit

### Operational Safety
- Test mode for safe strategy testing
- Comprehensive logging and monitoring
- State persistence for recovery
- Graceful shutdown handling

## Monitoring and Debugging

### Debug Mode
- Enable with `--debug` flag for detailed logging
- Shows technical indicator calculations
- Logs signal generation process
- Displays order execution details

### CLI Dashboard
- Real-time position monitoring
- Performance metrics display
- Live signal feed
- Interactive controls for bot management

When working with this codebase, always prioritize testing in safe mode before any live trading operations. The configuration system is comprehensive but requires careful attention to signal thresholds and risk parameters.